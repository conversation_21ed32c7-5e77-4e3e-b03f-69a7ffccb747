import { Entity, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Brand } from './brand.entity';
import { User } from './user.entity';

@Entity('monitoring_rules')
export class MonitoringRule {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ type: 'enum', enum: ['domain', 'social_media', 'email', 'mobile_app', 'marketplace', 'all'] })
    scope: 'domain' | 'social_media' | 'email' | 'mobile_app' | 'marketplace' | 'all';

    @Column({ type: 'jsonb' })
    conditions: {
        type: 'keyword' | 'regex' | 'similarity' | 'domain_pattern' | 'ip_range';
        operator: 'contains' | 'equals' | 'starts_with' | 'ends_with' | 'matches' | 'greater_than' | 'less_than';
        value: string | number;
        caseSensitive: boolean;
    }[];

    @Column({ type: 'jsonb', nullable: true })
    actions: {
        type: 'alert' | 'auto_takedown' | 'escalate' | 'block' | 'quarantine';
        parameters: { [key: string]: any };
    }[];

    @Column({ type: 'enum', enum: ['low', 'medium', 'high', 'critical'] })
    severity: 'low' | 'medium' | 'high' | 'critical';

    @Column({ default: true })
    isActive: boolean;

    @Column({ type: 'int', default: 0 })
    triggerCount: number;

    @Column({ type: 'timestamp', nullable: true })
    lastTriggered: Date;

    @Column({ type: 'jsonb', nullable: true })
    schedule: {
        frequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
        time: string;
        timezone: string;
    };

    @ManyToOne(() => Brand, brand => brand.monitoringRules)
    brand: Brand;

    @Column()
    brandId: string;

    @ManyToOne(() => User, user => user.monitoringRules)
    createdBy: User;

    @Column()
    createdById: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
