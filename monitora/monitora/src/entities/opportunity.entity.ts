import { Entity, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Contact } from './contact.entity';
import { User } from './user.entity';
import { PipelineStage } from './pipeline-stage.entity';

@Entity('opportunities')
export class Opportunity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @ManyToOne(() => Contact, contact => contact.opportunities)
    contact: Contact;

    @Column({ type: 'decimal', precision: 15, scale: 2 })
    value: number;

    @Column({ length: 3 })
    currency: string;

    @Column()
    probability: number;

    @ManyToOne(() => User, user => user.opportunities)
    owner: User;

    @Column({ name: 'due_date', type: 'date', nullable: true })
    dueDate: Date;

    @ManyToOne(() => PipelineStage, stage => stage.opportunities)
    stage: PipelineStage;

    @Column({ type: 'enum', enum: ['open', 'won', 'lost', 'abandoned'] })
    status: 'open' | 'won' | 'lost' | 'abandoned';

    @Column({ type: 'text', nullable: true })
    notes: string;
}