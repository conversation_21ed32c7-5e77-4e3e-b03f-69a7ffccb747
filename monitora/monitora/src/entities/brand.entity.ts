import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from "typeorm";
import { User } from "./user.entity";
import { DomainMonitor } from "./domain-monitor.entity";
import { SocialMediaMonitor } from "./social-media-monitor.entity";
import { ThreatDetection } from "./threat-detection.entity";
import { MonitoringRule } from "./monitoring-rule.entity";
import { Alert } from "./alert.entity";
import { TakedownRequest } from "./takedown-request.entity";

@Entity("brands")
export class Brand {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  name: string;

  @Column({ type: "text", nullable: true })
  description: string;

  @Column()
  primaryDomain: string;

  @Column("simple-array", { nullable: true })
  additionalDomains: string[];

  @Column("simple-array", { nullable: true })
  keywords: string[];

  @Column("simple-array", { nullable: true })
  protectedTerms: string[];

  @Column({ type: "text", nullable: true })
  logoUrl: string;

  @Column({
    default: "active",
    type: "enum",
    enum: ["active", "inactive", "suspended"],
  })
  status: "active" | "inactive" | "suspended";

  @Column({ type: "jsonb", nullable: true })
  socialMediaProfiles: {
    platform: string;
    username: string;
    url: string;
    verified: boolean;
  }[];

  @Column({ type: "jsonb", nullable: true })
  contactInfo: {
    email: string;
    phone: string;
    address: string;
  };

  @Column({ type: "jsonb", nullable: true })
  monitoringSettings: {
    domainMonitoring: boolean;
    socialMediaMonitoring: boolean;
    threatDetection: boolean;
    alertFrequency: "realtime" | "hourly" | "daily" | "weekly";
    sensitivityLevel: "low" | "medium" | "high";
  };

  @ManyToOne(() => User, (user) => user.brands)
  owner: User;

  @Column()
  ownerId: string;

  @OneToMany(() => DomainMonitor, (domainMonitor) => domainMonitor.brand)
  domainMonitors: DomainMonitor[];

  @OneToMany(
    () => SocialMediaMonitor,
    (socialMediaMonitor) => socialMediaMonitor.brand
  )
  socialMediaMonitors: SocialMediaMonitor[];

  @OneToMany(() => ThreatDetection, (threatDetection) => threatDetection.brand)
  threatDetections: ThreatDetection[];

  @OneToMany(() => MonitoringRule, (monitoringRule) => monitoringRule.brand)
  monitoringRules: MonitoringRule[];

  @OneToMany(() => Alert, (alert) => alert.brand)
  alerts: Alert[];

  @OneToMany(() => TakedownRequest, (takedownRequest) => takedownRequest.brand)
  takedownRequests: TakedownRequest[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
