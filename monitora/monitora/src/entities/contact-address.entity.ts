import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Contact } from './contact.entity';

@Entity()
export class ContactAddress {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  street: string;

  @Column()
  city: string;

  @Column()
  state: string;

  @Column()
  country: string;

  @Column()
  postalCode: string;

  @Column({ default: false })
  isPrimary: boolean;

  @ManyToOne(() => Contact, (contact: Contact) => contact.addresses)
  contact: Contact;
}
