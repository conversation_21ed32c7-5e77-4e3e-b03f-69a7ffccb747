import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Brand } from './brand.entity';
import { ThreatDetection } from './threat-detection.entity';
import { User } from './user.entity';

@Entity('alerts')
export class Alert {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'enum', enum: ['email', 'sms', 'push', 'webhook', 'slack', 'teams'] })
    type: 'email' | 'sms' | 'push' | 'webhook' | 'slack' | 'teams';

    @Column({ type: 'enum', enum: ['low', 'medium', 'high', 'critical'] })
    priority: 'low' | 'medium' | 'high' | 'critical';

    @Column({ type: 'enum', enum: ['pending', 'sent', 'delivered', 'failed', 'acknowledged'] })
    status: 'pending' | 'sent' | 'delivered' | 'failed' | 'acknowledged';

    @Column()
    title: string;

    @Column({ type: 'text' })
    message: string;

    @Column({ type: 'jsonb', nullable: true })
    metadata: {
        recipient: string;
        channel: string;
        templateId: string;
        variables: { [key: string]: any };
    };

    @Column({ type: 'jsonb', nullable: true })
    deliveryDetails: {
        attempts: number;
        lastAttempt: Date;
        errorMessage: string;
        deliveredAt: Date;
        acknowledgedAt: Date;
        acknowledgedBy: string;
    };

    @Column({ type: 'timestamp', nullable: true })
    scheduledFor: Date;

    @Column({ type: 'timestamp', nullable: true })
    sentAt: Date;

    @Column({ default: false })
    isRead: boolean;

    @Column({ default: false })
    isArchived: boolean;

    @ManyToOne(() => Brand, brand => brand.alerts)
    brand: Brand;

    @Column()
    brandId: string;

    @ManyToOne(() => ThreatDetection, threatDetection => threatDetection.alerts, { nullable: true })
    threatDetection: ThreatDetection;

    @Column({ nullable: true })
    threatDetectionId: string;

    @ManyToOne(() => User, user => user.alerts)
    recipient: User;

    @Column()
    recipientId: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
