import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Opportunity } from "./opportunity.entity";
import { Contact } from "./contact.entity";
import { Task } from "./task.entity";
import { Brand } from "./brand.entity";
import { Alert } from "./alert.entity";
import { TakedownRequest } from "./takedown-request.entity";
import { MonitoringRule } from "./monitoring-rule.entity";

@Entity("users")
export class User {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column()
  username: string;

  @Column()
  name: string;

  @Column({ default: "active", type: "enum", enum: ["active", "inactive"] })
  status: "active" | "inactive";

  @OneToMany(() => Task, (task: Task) => task.assignedTo)
  tasks: Task[];

  @OneToMany(() => Opportunity, (opportunity) => opportunity.owner)
  opportunities: Opportunity[];

  @OneToMany(() => Contact, (contact) => contact.assignedTo)
  contacts: Contact[];

  @OneToMany(() => Brand, (brand) => brand.owner)
  brands: Brand[];

  @OneToMany(() => Alert, (alert) => alert.recipient)
  alerts: Alert[];

  @OneToMany(
    () => TakedownRequest,
    (takedownRequest) => takedownRequest.assignedTo
  )
  takedownRequests: TakedownRequest[];

  @OneToMany(() => MonitoringRule, (monitoringRule) => monitoringRule.createdBy)
  monitoringRules: MonitoringRule[];
}
