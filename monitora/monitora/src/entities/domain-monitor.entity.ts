import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Brand } from './brand.entity';
import { ThreatDetection } from './threat-detection.entity';

@Entity('domain_monitors')
export class DomainMonitor {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    domain: string;

    @Column({ type: 'enum', enum: ['suspicious', 'confirmed_threat', 'false_positive', 'monitoring', 'taken_down'] })
    status: 'suspicious' | 'confirmed_threat' | 'false_positive' | 'monitoring' | 'taken_down';

    @Column({ type: 'float', default: 0 })
    similarityScore: number;

    @Column({ type: 'jsonb', nullable: true })
    whoisData: {
        registrar: string;
        registrationDate: Date;
        expirationDate: Date;
        registrantName: string;
        registrantEmail: string;
        registrantCountry: string;
        nameServers: string[];
    };

    @Column({ type: 'jsonb', nullable: true })
    dnsRecords: {
        type: string;
        value: string;
        ttl: number;
    }[];

    @Column({ type: 'jsonb', nullable: true })
    websiteData: {
        title: string;
        description: string;
        content: string;
        screenshots: string[];
        technologies: string[];
        ssl: {
            valid: boolean;
            issuer: string;
            expirationDate: Date;
        };
    };

    @Column({ type: 'jsonb', nullable: true })
    riskFactors: {
        factor: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        description: string;
    }[];

    @Column({ type: 'enum', enum: ['typosquatting', 'homograph', 'subdomain', 'tld_variation', 'keyword_stuffing', 'phishing', 'malware'] })
    detectionType: 'typosquatting' | 'homograph' | 'subdomain' | 'tld_variation' | 'keyword_stuffing' | 'phishing' | 'malware';

    @Column({ type: 'text', nullable: true })
    notes: string;

    @Column({ type: 'timestamp', nullable: true })
    firstDetected: Date;

    @Column({ type: 'timestamp', nullable: true })
    lastChecked: Date;

    @Column({ default: true })
    isActive: boolean;

    @ManyToOne(() => Brand, brand => brand.domainMonitors)
    brand: Brand;

    @Column()
    brandId: string;

    @OneToMany(() => ThreatDetection, threatDetection => threatDetection.domainMonitor)
    threatDetections: ThreatDetection[];

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
