import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { ProductImage } from './product-image.entity';
import { ProductFeature } from './product-feature.entity';

@Entity('products')
export class Product {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ unique: true, nullable: true })
    sku: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ nullable: true })
    category: string;

    @Column({ type: 'enum', enum: ['physical', 'digital', 'service', 'subscription'] })
    type: 'physical' | 'digital' | 'service' | 'subscription';

    @Column({ type: 'decimal', precision: 15, scale: 2 })
    price: number;

    @Column({ type: 'enum', enum: ['one-time', 'monthly', 'annually', 'custom'] })
    billing: 'one-time' | 'monthly' | 'annually' | 'custom';

    @Column({ name: 'tax_class', nullable: true })
    taxClass: string;

    @Column({ default: 'active' })
    status: string;

    @OneToMany(() => ProductImage, image => image.product)
    images: ProductImage[];

    @OneToMany(() => ProductFeature, feature => feature.product)
    features: ProductFeature[];
}