import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from './user.entity';

@Entity('tasks')
export class Task {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    title: string;

    @Column({ type: 'text', nullable: true })
    description: string;

    @Column({ name: 'due_date', nullable: true })
    dueDate: Date;

    @Column({ type: 'enum', enum: ['high', 'medium', 'low'] })
    priority: 'high' | 'medium' | 'low';

    @Column({ type: 'enum', enum: ['pending', 'in_progress', 'completed', 'overdue'] })
    status: 'pending' | 'in_progress' | 'completed' | 'overdue';

    @Column({ type: 'enum', enum: ['call', 'email', 'meeting', 'task'] })
    type: 'call' | 'email' | 'meeting' | 'task';

    @ManyToOne(() => User, user => user.tasks)
    assignedTo: User;

    @Column({ name: 'related_type', type: 'enum', enum: ['contact', 'opportunity', 'company'] })
    relatedType: 'contact' | 'opportunity' | 'company';

    @Column({ name: 'related_id' })
    relatedId: string;

    @Column({ nullable: true })
    reminder: Date;

    @Column({ name: 'completed_at', nullable: true })
    completedAt: Date;

    @ManyToOne(() => User)
    createdBy: User;

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;
}