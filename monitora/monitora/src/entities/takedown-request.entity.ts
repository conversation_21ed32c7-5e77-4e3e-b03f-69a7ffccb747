import { Entity, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Brand } from './brand.entity';
import { ThreatDetection } from './threat-detection.entity';
import { User } from './user.entity';

@Entity('takedown_requests')
export class TakedownRequest {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'enum', enum: ['domain_registrar', 'hosting_provider', 'social_media', 'search_engine', 'app_store', 'marketplace', 'legal'] })
    targetType: 'domain_registrar' | 'hosting_provider' | 'social_media' | 'search_engine' | 'app_store' | 'marketplace' | 'legal';

    @Column()
    targetName: string;

    @Column({ type: 'text', nullable: true })
    targetContact: string;

    @Column({ type: 'enum', enum: ['submitted', 'acknowledged', 'under_review', 'approved', 'rejected', 'completed', 'escalated'] })
    status: 'submitted' | 'acknowledged' | 'under_review' | 'approved' | 'rejected' | 'completed' | 'escalated';

    @Column({ type: 'enum', enum: ['dmca', 'trademark', 'copyright', 'abuse', 'phishing', 'malware', 'terms_violation'] })
    requestType: 'dmca' | 'trademark' | 'copyright' | 'abuse' | 'phishing' | 'malware' | 'terms_violation';

    @Column({ type: 'text' })
    description: string;

    @Column({ type: 'jsonb', nullable: true })
    evidence: {
        screenshots: string[];
        documents: string[];
        urls: string[];
        technicalEvidence: { [key: string]: any };
    };

    @Column({ type: 'jsonb', nullable: true })
    legalBasis: {
        trademarkNumbers: string[];
        copyrightNumbers: string[];
        legalDocuments: string[];
        jurisdiction: string;
    };

    @Column({ type: 'text', nullable: true })
    requestDetails: string;

    @Column({ type: 'jsonb', nullable: true })
    communication: {
        date: Date;
        type: 'email' | 'phone' | 'portal' | 'letter';
        direction: 'sent' | 'received';
        subject: string;
        content: string;
        attachments: string[];
    }[];

    @Column({ type: 'jsonb', nullable: true })
    timeline: {
        date: Date;
        event: string;
        description: string;
        actor: string;
    }[];

    @Column({ type: 'text', nullable: true })
    outcome: string;

    @Column({ type: 'text', nullable: true })
    notes: string;

    @Column({ type: 'timestamp', nullable: true })
    submittedAt: Date;

    @Column({ type: 'timestamp', nullable: true })
    acknowledgedAt: Date;

    @Column({ type: 'timestamp', nullable: true })
    completedAt: Date;

    @Column({ type: 'timestamp', nullable: true })
    dueDate: Date;

    @Column({ type: 'enum', enum: ['low', 'medium', 'high', 'urgent'] })
    priority: 'low' | 'medium' | 'high' | 'urgent';

    @ManyToOne(() => Brand, brand => brand.takedownRequests)
    brand: Brand;

    @Column()
    brandId: string;

    @ManyToOne(() => ThreatDetection, threatDetection => threatDetection.takedownRequests)
    threatDetection: ThreatDetection;

    @Column()
    threatDetectionId: string;

    @ManyToOne(() => User, user => user.takedownRequests)
    assignedTo: User;

    @Column()
    assignedToId: string;

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
