import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, OneToMany } from 'typeorm';
import { ContactAddress } from './contact-address.entity';
import { ContactSocialProfile } from './contact-social-profile.entity';
import { Opportunity } from './opportunity.entity';
import { User } from './user.entity';

@Entity('contacts')
export class Contact {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    name: string;

    @Column({ nullable: true })
    company: string;

    @Column({ nullable: true })
    email: string;

    @Column({ nullable: true })
    phone: string;

    @Column({ type: 'enum', enum: ['lead', 'customer', 'prospect'] })
    type: 'lead' | 'customer' | 'prospect';

    @Column({ type: 'enum', enum: ['active', 'inactive', 'new', 'qualified'] })
    status: 'active' | 'inactive' | 'new' | 'qualified';

    @ManyToOne(() => User, user => user.contacts)
    assignedTo: User;

    @Column({ name: 'last_contact', nullable: true })
    lastContact: Date;

    @Column({ name: 'avatar_url', nullable: true })
    avatarUrl: string;

    @Column({ type: 'text', nullable: true })
    notes: string;

    @OneToMany(() => ContactAddress, address => address.contact)
    addresses: ContactAddress[];

    @OneToMany(() => ContactSocialProfile, socialProfile => socialProfile.contact)
    socialProfiles: ContactSocialProfile[];

    @OneToMany(() => Opportunity, opportunity => opportunity.contact)
    opportunities: Opportunity[];
}
