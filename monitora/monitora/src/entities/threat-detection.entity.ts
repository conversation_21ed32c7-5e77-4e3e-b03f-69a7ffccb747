import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Brand } from './brand.entity';
import { DomainMonitor } from './domain-monitor.entity';
import { SocialMediaMonitor } from './social-media-monitor.entity';
import { Alert } from './alert.entity';
import { TakedownRequest } from './takedown-request.entity';

@Entity('threat_detections')
export class ThreatDetection {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'enum', enum: ['domain', 'social_media', 'email', 'mobile_app', 'marketplace'] })
    sourceType: 'domain' | 'social_media' | 'email' | 'mobile_app' | 'marketplace';

    @Column()
    sourceUrl: string;

    @Column({ type: 'enum', enum: ['low', 'medium', 'high', 'critical'] })
    severity: 'low' | 'medium' | 'high' | 'critical';

    @Column({ type: 'enum', enum: ['pending', 'investigating', 'confirmed', 'false_positive', 'resolved', 'escalated'] })
    status: 'pending' | 'investigating' | 'confirmed' | 'false_positive' | 'resolved' | 'escalated';

    @Column({ type: 'float', default: 0 })
    confidenceScore: number;

    @Column({ type: 'enum', enum: ['typosquatting', 'phishing', 'malware', 'impersonation', 'trademark_infringement', 'counterfeit', 'scam', 'fraud'] })
    threatType: 'typosquatting' | 'phishing' | 'malware' | 'impersonation' | 'trademark_infringement' | 'counterfeit' | 'scam' | 'fraud';

    @Column({ type: 'text' })
    description: string;

    @Column({ type: 'jsonb', nullable: true })
    evidence: {
        screenshots: string[];
        textContent: string;
        metadata: { [key: string]: any };
        technicalDetails: { [key: string]: any };
    };

    @Column({ type: 'jsonb', nullable: true })
    riskAssessment: {
        impactLevel: 'low' | 'medium' | 'high' | 'critical';
        affectedUsers: number;
        potentialDamage: string;
        urgency: 'low' | 'medium' | 'high' | 'critical';
        businessImpact: string;
    };

    @Column({ type: 'jsonb', nullable: true })
    detectionMethods: {
        method: string;
        confidence: number;
        details: string;
    }[];

    @Column({ type: 'jsonb', nullable: true })
    mitigationSteps: {
        step: string;
        status: 'pending' | 'in_progress' | 'completed' | 'failed';
        assignedTo: string;
        dueDate: Date;
        notes: string;
    }[];

    @Column({ type: 'text', nullable: true })
    analystNotes: string;

    @Column({ type: 'timestamp', nullable: true })
    firstDetected: Date;

    @Column({ type: 'timestamp', nullable: true })
    lastUpdated: Date;

    @Column({ type: 'timestamp', nullable: true })
    resolvedAt: Date;

    @ManyToOne(() => Brand, brand => brand.threatDetections)
    brand: Brand;

    @Column()
    brandId: string;

    @ManyToOne(() => DomainMonitor, domainMonitor => domainMonitor.threatDetections, { nullable: true })
    domainMonitor: DomainMonitor;

    @Column({ nullable: true })
    domainMonitorId: string;

    @ManyToOne(() => SocialMediaMonitor, socialMediaMonitor => socialMediaMonitor.threatDetections, { nullable: true })
    socialMediaMonitor: SocialMediaMonitor;

    @Column({ nullable: true })
    socialMediaMonitorId: string;

    @OneToMany(() => Alert, alert => alert.threatDetection)
    alerts: Alert[];

    @OneToMany(() => TakedownRequest, takedownRequest => takedownRequest.threatDetection)
    takedownRequests: TakedownRequest[];

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
