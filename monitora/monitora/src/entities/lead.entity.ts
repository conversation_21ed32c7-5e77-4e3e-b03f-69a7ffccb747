import { En<PERSON>ty, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { User } from './user.entity';

@Entity('leads')
export class Lead {
  @Column()
  source: string;

  @Column({ type: 'enum', enum: ['new', 'contacted', 'qualified', 'unqualified'] })
  status: string;

  @Column()
  score: number;

  @ManyToOne(() => User)
  assignedTo: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}