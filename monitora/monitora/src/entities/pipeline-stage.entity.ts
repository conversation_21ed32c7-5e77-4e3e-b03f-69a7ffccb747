import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Opportunity } from './opportunity.entity';

@Entity('pipeline_stages')
export class PipelineStage {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column()
    title: string;

    @Column({ nullable: true })
    color: string;

    @Column({ name: 'order_index' })
    orderIndex: number;

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;

    @OneToMany(() => Opportunity, opportunity => opportunity.stage)
    opportunities: Opportunity[];
}