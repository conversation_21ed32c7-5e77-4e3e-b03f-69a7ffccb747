import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";
import { Role } from "./role.entity";

@Entity("permissions")
export class Permission {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ unique: true })
  name: string;

  @Column()
  resource: string; // e.g., 'brands', 'users', 'threats', 'reports'

  @Column()
  action: string; // e.g., 'create', 'read', 'update', 'delete', 'manage'

  @Column({ type: "text", nullable: true })
  description: string;

  @Column({ default: false })
  isSystemPermission: boolean; // Cannot be deleted

  @ManyToMany(() => Role, (role) => role.permissions)
  roles: Role[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
