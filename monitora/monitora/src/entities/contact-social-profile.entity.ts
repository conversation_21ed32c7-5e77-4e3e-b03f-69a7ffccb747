import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne } from 'typeorm';
import { Contact } from './contact.entity';

@Entity()
export class ContactSocialProfile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  platform: string;

  @Column()
  profileUrl: string;

  @Column({ nullable: true })
  username: string;

  @ManyToOne(() => Contact, contact => contact.socialProfiles)
  contact: Contact;
}