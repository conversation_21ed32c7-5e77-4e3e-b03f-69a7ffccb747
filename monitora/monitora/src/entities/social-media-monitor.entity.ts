import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Brand } from './brand.entity';
import { ThreatDetection } from './threat-detection.entity';

@Entity('social_media_monitors')
export class SocialMediaMonitor {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @Column({ type: 'enum', enum: ['facebook', 'instagram', 'twitter', 'linkedin', 'tiktok', 'youtube', 'snapchat', 'pinterest'] })
    platform: 'facebook' | 'instagram' | 'twitter' | 'linkedin' | 'tiktok' | 'youtube' | 'snapchat' | 'pinterest';

    @Column()
    contentUrl: string;

    @Column({ type: 'enum', enum: ['post', 'ad', 'profile', 'page', 'video', 'story'] })
    contentType: 'post' | 'ad' | 'profile' | 'page' | 'video' | 'story';

    @Column({ type: 'text' })
    content: string;

    @Column({ nullable: true })
    authorUsername: string;

    @Column({ nullable: true })
    authorDisplayName: string;

    @Column({ type: 'jsonb', nullable: true })
    authorProfile: {
        followers: number;
        following: number;
        verified: boolean;
        profileImage: string;
        bio: string;
        location: string;
        joinDate: Date;
    };

    @Column({ type: 'enum', enum: ['suspicious', 'confirmed_threat', 'false_positive', 'monitoring', 'reported', 'taken_down'] })
    status: 'suspicious' | 'confirmed_threat' | 'false_positive' | 'monitoring' | 'reported' | 'taken_down';

    @Column({ type: 'float', default: 0 })
    similarityScore: number;

    @Column({ type: 'jsonb', nullable: true })
    engagement: {
        likes: number;
        shares: number;
        comments: number;
        views: number;
        reactions: { [key: string]: number };
    };

    @Column({ type: 'jsonb', nullable: true })
    mediaUrls: string[];

    @Column({ type: 'jsonb', nullable: true })
    hashtags: string[];

    @Column({ type: 'jsonb', nullable: true })
    mentions: string[];

    @Column({ type: 'jsonb', nullable: true })
    riskFactors: {
        factor: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        description: string;
    }[];

    @Column({ type: 'enum', enum: ['impersonation', 'trademark_infringement', 'fake_promotion', 'phishing', 'scam', 'counterfeit'] })
    detectionType: 'impersonation' | 'trademark_infringement' | 'fake_promotion' | 'phishing' | 'scam' | 'counterfeit';

    @Column({ type: 'text', nullable: true })
    notes: string;

    @Column({ type: 'timestamp', nullable: true })
    publishedAt: Date;

    @Column({ type: 'timestamp', nullable: true })
    firstDetected: Date;

    @Column({ type: 'timestamp', nullable: true })
    lastChecked: Date;

    @Column({ default: true })
    isActive: boolean;

    @ManyToOne(() => Brand, brand => brand.socialMediaMonitors)
    brand: Brand;

    @Column()
    brandId: string;

    @OneToMany(() => ThreatDetection, threatDetection => threatDetection.socialMediaMonitor)
    threatDetections: ThreatDetection[];

    @CreateDateColumn()
    createdAt: Date;

    @UpdateDateColumn()
    updatedAt: Date;
}
