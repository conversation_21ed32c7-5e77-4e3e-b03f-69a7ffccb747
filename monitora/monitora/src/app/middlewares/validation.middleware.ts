import { Request, Response, NextFunction } from 'express';
import { validate, ValidationError } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { ValidationException } from '../exceptions/http.exception';

export function validateRequest(type: any) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const dto = plainToClass(type, req.body);
    const errors = await validate(dto);
    
    if (errors.length > 0) {
      const formattedErrors = errors.map((error: ValidationError) => ({
        field: error.property,
        constraints: error.constraints
      }));
      throw new ValidationException(formattedErrors);
    }
    
    req.body = dto;
    next();
  };
}