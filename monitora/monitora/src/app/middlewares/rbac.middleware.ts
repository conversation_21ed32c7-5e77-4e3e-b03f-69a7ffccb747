import { Request, Response, NextFunction } from "express";
import { RBACService } from "../services/rbac.service";
import { UserRepository } from "../repositories/user.repository";
import { RoleRepository } from "../repositories/role.repository";
import { PermissionRepository } from "../repositories/permission.repository";
import "../../types/express";

export interface RBACOptions {
  resource: string;
  action: string;
  allowSelf?: boolean;
}

export const checkPermission = (resource: string, action: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = req.user || req.currentUser;

      if (!user) {
        return res.status(401).json({ error: "Authentication required" });
      }

      // Create RBAC service with repositories
      const userRepository = new UserRepository();
      const roleRepository = new RoleRepository();
      const permissionRepository = new PermissionRepository();
      const rbacService = new RBACService(
        userRepository,
        roleRepository,
        permissionRepository
      );

      const hasPermission = await rbacService.checkPermission(
        String(user.id),
        resource,
        action
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required: `${resource}:${action}`,
        });
      }

      next();
    } catch (error) {
      console.error("RBAC middleware error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  };
};

export function requirePermission(options: RBACOptions) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = req.user || req.currentUser;

      if (!user) {
        return res.status(401).json({ error: "Authentication required" });
      }

      // Create RBAC service with repositories
      const userRepository = new UserRepository();
      const roleRepository = new RoleRepository();
      const permissionRepository = new PermissionRepository();
      const rbacService = new RBACService(
        userRepository,
        roleRepository,
        permissionRepository
      );

      // Check if user has the required permission
      const hasPermission = await rbacService.checkPermission(
        String(user.id),
        options.resource,
        options.action
      );

      // Handle allowSelf option - user can access their own resources
      if (!hasPermission && options.allowSelf) {
        const resourceId = req.params.id || req.params.userId;
        if (resourceId === user.id) {
          return next();
        }
      }

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required: `${options.resource}:${options.action}`,
        });
      }

      next();
    } catch (error) {
      console.error("RBAC middleware error:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  };
}

// Convenience functions for common permission checks
export const requireSystemAdmin = () =>
  requirePermission({ resource: "system", action: "manage" });
export const requireAdmin = () =>
  requirePermission({ resource: "users", action: "manage" });
export const requireBrandManage = () =>
  requirePermission({ resource: "brands", action: "manage" });
export const requireBrandRead = () =>
  requirePermission({ resource: "brands", action: "read" });
export const requireThreatManage = () =>
  requirePermission({ resource: "threats", action: "manage" });
export const requireThreatRead = () =>
  requirePermission({ resource: "threats", action: "read" });
export const requireReportRead = () =>
  requirePermission({ resource: "reports", action: "read" });
