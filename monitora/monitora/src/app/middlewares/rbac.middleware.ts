import { Request, Response, NextFunction } from 'express';
import { RBACService } from '../services/rbac.service';

export const checkPermission = (resource: string, action: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const rbacService = new RBACService();
    const hasPermission = await rbacService.checkPermission(
      req.currentUser.id,
      resource,
      action
    );

    if (!hasPermission) {
      return res.status(403).json({ 
        message: 'Insufficient permissions' 
      });
    }

    next();
  };
};