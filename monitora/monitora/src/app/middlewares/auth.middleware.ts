import { Request, Response, NextFunction } from 'express';
import { container } from '@core/di/container';
import { AuthService } from '../services/auth.service';

export function auth(req: Request, res: Response, next: NextFunction) {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ message: 'Unauthorized - No Token Provided' });
  }

  const token = authHeader.split(' ')[1];
  const authService = container.resolve(AuthService);
  const decoded = authService.verifyToken(token);

  if (!decoded) {
    return res.status(401).json({ message: 'Unauthorized - Invalid Token' });
  }

  req.currentUser = decoded as { id: number; username: string };

  next();
}