import { Request, Response, NextFunction } from 'express';
import { HttpException } from '../exceptions/http.exception';
import { Logger } from '../utils/logger';

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const logger = new Logger('ErrorHandler');
  logger.error(error);

  if (error instanceof HttpException) {
    return res.status(error.status).json({
      status: error.status,
      message: error.message,
      errors: error.errors
    });
  }

  return res.status(500).json({
    status: 500,
    message: 'Internal server error',
    errors: process.env.NODE_ENV === 'development' ? error.stack : undefined
  });
};
