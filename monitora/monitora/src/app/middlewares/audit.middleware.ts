import { Request, Response, NextFunction } from 'express';
import { ActivityService } from '../services/activity.service';

export const audit = (activityType: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    res.send = function (body) {
      const activityService = new ActivityService();
      activityService.create({
        type: activityType,
        userId: req.currentUser?.id,
        details: {
          method: req.method,
          path: req.path,
          body: req.body,
          response: body
        }
      });
      return originalSend.call(this, body);
    };
    next();
  };
};