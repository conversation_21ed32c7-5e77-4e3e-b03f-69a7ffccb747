import { Provider } from '@core/decorators/decorators';
import { BaseRepository } from '@core/repositories/base.repository';
import { Product } from '../../entities/product.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class ProductRepository extends BaseRepository<Product> {
  constructor() {
    super(Product);
  }

  @Mock('ProductRepository-findBySku')
  async findBySku(sku: string): Promise<Product | null> {
    return this.repository.findOne({ where: { sku } });
  }

  @Mock('ProductRepository-findWithFeatures')
  async findWithFeatures(id: string): Promise<Product | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['features', 'images']
    });
  }

  @Mock('ProductRepository-findByCategory')
  async findByCategory(category: string): Promise<Product[]> {
    return this.repository.find({ where: { category } });
  }
}