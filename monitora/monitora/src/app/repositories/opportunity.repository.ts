import { Provider } from '@core/decorators/decorators';
import { BaseRepository } from '@core/repositories/base.repository';
import { Opportunity } from '../../entities/opportunity.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class OpportunityRepository extends BaseRepository<Opportunity> {
  constructor() {
    super(Opportunity);
  }

  @Mock('OpportunityRepository-findByOwner')
  async findByOwner(userId: string): Promise<Opportunity[]> {
    return this.repository.find({
      where: { owner: { id: userId } },
      relations: ['owner']
    });
  }

  @Mock('OpportunityRepository-findByStage')
  async findByStage(stage: string): Promise<Opportunity[]> {
    return this.repository.find({
      where: { stage: { id: stage } },
      relations: ['stage']
    });
  }
}
