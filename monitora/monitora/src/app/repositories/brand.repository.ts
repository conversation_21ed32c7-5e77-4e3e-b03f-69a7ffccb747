import { Provider } from '@core/decorators/decorators';
import { BaseRepository } from '@core/repositories/base.repository';
import { Brand } from '../../entities/brand.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class BrandRepository extends BaseRepository<Brand> {
  constructor() {
    super(Brand);
  }

  @Mock('BrandRepository-findByOwner')
  async findByOwner(ownerId: string): Promise<Brand[]> {
    return this.repository.find({ 
      where: { ownerId },
      relations: ['domainMonitors', 'socialMediaMonitors', 'threatDetections']
    });
  }

  @Mock('BrandRepository-findByDomain')
  async findByDomain(domain: string): Promise<Brand | null> {
    return this.repository.findOne({ 
      where: { primaryDomain: domain }
    });
  }

  @Mock('BrandRepository-findByStatus')
  async findByStatus(status: Brand['status']): Promise<Brand[]> {
    return this.repository.find({ 
      where: { status },
      relations: ['domainMonitors', 'socialMediaMonitors']
    });
  }

  @Mock('BrandRepository-findWithMonitoring')
  async findWithMonitoring(id: string): Promise<Brand | null> {
    return this.repository.findOne({
      where: { id },
      relations: [
        'domainMonitors',
        'socialMediaMonitors',
        'threatDetections',
        'monitoringRules',
        'alerts',
        'takedownRequests'
      ]
    });
  }

  @Mock('BrandRepository-getBrandStats')
  async getBrandStats(id: string): Promise<{
    totalThreats: number;
    activeThreats: number;
    resolvedThreats: number;
    domainMonitors: number;
    socialMediaMonitors: number;
    alerts: number;
    takedownRequests: number;
  } | null> {
    const brand = await this.repository
      .createQueryBuilder('brand')
      .leftJoinAndSelect('brand.threatDetections', 'threats')
      .leftJoinAndSelect('brand.domainMonitors', 'domains')
      .leftJoinAndSelect('brand.socialMediaMonitors', 'social')
      .leftJoinAndSelect('brand.alerts', 'alerts')
      .leftJoinAndSelect('brand.takedownRequests', 'takedowns')
      .where('brand.id = :id', { id })
      .getOne();

    if (!brand) return null;

    const totalThreats = brand.threatDetections?.length || 0;
    const activeThreats = brand.threatDetections?.filter(t => 
      ['pending', 'investigating', 'confirmed'].includes(t.status)
    ).length || 0;
    const resolvedThreats = brand.threatDetections?.filter(t => 
      t.status === 'resolved'
    ).length || 0;

    return {
      totalThreats,
      activeThreats,
      resolvedThreats,
      domainMonitors: brand.domainMonitors?.length || 0,
      socialMediaMonitors: brand.socialMediaMonitors?.length || 0,
      alerts: brand.alerts?.length || 0,
      takedownRequests: brand.takedownRequests?.length || 0
    };
  }

  @Mock('BrandRepository-searchByKeyword')
  async searchByKeyword(keyword: string): Promise<Brand[]> {
    return this.repository
      .createQueryBuilder('brand')
      .where('brand.name ILIKE :keyword', { keyword: `%${keyword}%` })
      .orWhere('brand.keywords @> :keywordArray', { keywordArray: [keyword.toLowerCase()] })
      .orWhere('brand.protectedTerms @> :keywordArray', { keywordArray: [keyword.toLowerCase()] })
      .getMany();
  }

  @Mock('BrandRepository-findBrandsNeedingMonitoring')
  async findBrandsNeedingMonitoring(): Promise<Brand[]> {
    return this.repository
      .createQueryBuilder('brand')
      .where('brand.status = :status', { status: 'active' })
      .andWhere("brand.monitoringSettings->>'domainMonitoring' = 'true'")
      .orWhere("brand.monitoringSettings->>'socialMediaMonitoring' = 'true'")
      .getMany();
  }
}
