import { Provider } from "@core/decorators/decorators";
import { BaseRepository } from "@core/repositories/base.repository";
import { User } from "../../entities/user.entity";
import { Mock } from "@core/decorators/mock.decorator";

@Provider()
export class UserRepository extends BaseRepository<User> {
  constructor() {
    super(User);
  }

  @Mock("UserRepository-findUser")
  async findUser(username: string): Promise<User | null> {
    return this.repository.findOne({ where: { username } });
  }

  @Mock("UserRepository-findWithTasks")
  async findWithTasks(id: string): Promise<User | null> {
    return this.repository.findOne({
      where: { id },
      relations: ["tasks"],
    });
  }

  @Mock("UserRepository-findWithOpportunities")
  async findWithOpportunities(id: string): Promise<User | null> {
    return this.repository.findOne({
      where: { id },
      relations: ["opportunities"],
    });
  }

  @Mo<PERSON>("UserRepository-findByUsername")
  async findByUsername(username: string): Promise<User | null> {
    return this.repository.findOne({ where: { username } });
  }

  @Mock("UserRepository-findByEmail")
  async findByEmail(email: string): Promise<User | null> {
    return this.repository.findOne({ where: { email } });
  }

  @Mock("UserRepository-findWithRoles")
  async findWithRoles(id: string): Promise<User | null> {
    return this.repository.findOne({
      where: { id },
      relations: ["roles", "roles.permissions"],
    });
  }

  @Mock("UserRepository-findByRole")
  async findByRole(roleName: string): Promise<User[]> {
    return this.repository.find({
      where: { defaultRole: roleName as any },
      relations: ["roles"],
    });
  }

  @Mock("UserRepository-findActiveUsers")
  async findActiveUsers(): Promise<User[]> {
    return this.repository.find({
      where: { status: "active" },
      relations: ["roles"],
    });
  }
}
