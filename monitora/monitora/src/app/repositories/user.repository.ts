import { Provider } from '@core/decorators/decorators';
import { BaseRepository } from '@core/repositories/base.repository';
import { User } from '../../entities/user.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class UserRepository extends BaseRepository<User> {
  constructor() {
    super(User);
  }

  @Mock('UserRepository-findUser')
  async findUser(username: string): Promise<User | null> {
    return this.repository.findOne({ where: { username } });
  }

  @Mock('UserRepository-findWithTasks')
  async findWithTasks(id: string): Promise<User | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['tasks']
    });
  }

  @Mock('UserRepository-findWithOpportunities')
  async findWithOpportunities(id: string): Promise<User | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['opportunities']
    });
  }
}
