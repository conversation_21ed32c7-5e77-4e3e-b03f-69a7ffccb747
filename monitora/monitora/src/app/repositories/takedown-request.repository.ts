import { Provider } from '@core/decorators/decorators';
import { BaseRepository } from '@core/repositories/base.repository';
import { TakedownRequest } from '../../entities/takedown-request.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class TakedownRequestRepository extends BaseRepository<TakedownRequest> {
  constructor() {
    super(TakedownRequest);
  }

  @Mock('TakedownRequestRepository-findByBrand')
  async findByBrand(brandId: string): Promise<TakedownRequest[]> {
    return this.repository.find({
      where: { brandId },
      relations: ['brand', 'threatDetection', 'assignedTo'],
      order: { createdAt: 'DESC' }
    });
  }

  @Mock('TakedownRequestRepository-findByStatus')
  async findByStatus(status: TakedownRequest['status']): Promise<TakedownRequest[]> {
    return this.repository.find({
      where: { status },
      relations: ['brand', 'threatDetection'],
      order: { createdAt: 'DESC' }
    });
  }

  @Mock('TakedownRequestRepository-findByTargetType')
  async findByTargetType(targetType: TakedownRequest['targetType']): Promise<TakedownRequest[]> {
    return this.repository.find({
      where: { targetType },
      relations: ['brand', 'threatDetection'],
      order: { createdAt: 'DESC' }
    });
  }

  @Mock('TakedownRequestRepository-findPendingRequests')
  async findPendingRequests(): Promise<TakedownRequest[]> {
    return this.repository.find({
      where: { 
        status: 'submitted'
      },
      relations: ['brand', 'threatDetection'],
      order: { priority: 'DESC', createdAt: 'ASC' }
    });
  }

  @Mock('TakedownRequestRepository-findOverdueRequests')
  async findOverdueRequests(): Promise<TakedownRequest[]> {
    const now = new Date();
    
    return this.repository
      .createQueryBuilder('takedown')
      .leftJoinAndSelect('takedown.brand', 'brand')
      .leftJoinAndSelect('takedown.threatDetection', 'threat')
      .where('takedown.dueDate < :now', { now })
      .andWhere('takedown.status NOT IN (:...completedStatuses)', { 
        completedStatuses: ['completed', 'rejected'] 
      })
      .orderBy('takedown.dueDate', 'ASC')
      .getMany();
  }

  @Mock('TakedownRequestRepository-getStatsByBrand')
  async getStatsByBrand(brandId: string): Promise<{
    total: number;
    byStatus: { [key: string]: number };
    byTargetType: { [key: string]: number };
    byPriority: { [key: string]: number };
    averageResolutionTime: number;
  }> {
    const requests = await this.findByBrand(brandId);
    
    const stats = {
      total: requests.length,
      byStatus: {} as { [key: string]: number },
      byTargetType: {} as { [key: string]: number },
      byPriority: {} as { [key: string]: number },
      averageResolutionTime: 0
    };

    let totalResolutionTime = 0;
    let completedRequests = 0;

    requests.forEach(request => {
      // Count by status
      stats.byStatus[request.status] = (stats.byStatus[request.status] || 0) + 1;
      
      // Count by target type
      stats.byTargetType[request.targetType] = (stats.byTargetType[request.targetType] || 0) + 1;
      
      // Count by priority
      stats.byPriority[request.priority] = (stats.byPriority[request.priority] || 0) + 1;

      // Calculate resolution time for completed requests
      if (request.status === 'completed' && request.submittedAt && request.completedAt) {
        const resolutionTime = request.completedAt.getTime() - request.submittedAt.getTime();
        totalResolutionTime += resolutionTime;
        completedRequests++;
      }
    });

    // Calculate average resolution time in hours
    if (completedRequests > 0) {
      stats.averageResolutionTime = Math.round(totalResolutionTime / completedRequests / (1000 * 60 * 60));
    }

    return stats;
  }

  @Mock('TakedownRequestRepository-updateBulkStatus')
  async updateBulkStatus(requestIds: string[], status: TakedownRequest['status']): Promise<void> {
    const updateData: any = { status };
    
    if (status === 'completed') {
      updateData.completedAt = new Date();
    }

    await this.repository
      .createQueryBuilder()
      .update(TakedownRequest)
      .set(updateData)
      .where('id IN (:...ids)', { ids: requestIds })
      .execute();
  }

  @Mock('TakedownRequestRepository-findByThreatDetection')
  async findByThreatDetection(threatDetectionId: string): Promise<TakedownRequest[]> {
    return this.repository.find({
      where: { threatDetectionId },
      relations: ['brand', 'threatDetection'],
      order: { createdAt: 'DESC' }
    });
  }
}
