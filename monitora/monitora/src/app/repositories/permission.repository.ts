import { Provider } from "@core/decorators/decorators";
import { BaseRepository } from "@core/repositories/base.repository";
import { Permission } from "../../entities/permission.entity";

@Provider()
export class PermissionRepository extends BaseRepository<Permission> {
  constructor() {
    super(Permission);
  }

  async findByResourceAndAction(resource: string, action: string): Promise<Permission | null> {
    return this.repository.findOne({ 
      where: { resource, action } 
    });
  }

  async findByResource(resource: string): Promise<Permission[]> {
    return this.repository.find({ 
      where: { resource },
      relations: ["roles"]
    });
  }

  async findSystemPermissions(): Promise<Permission[]> {
    return this.repository.find({
      where: { isSystemPermission: true }
    });
  }

  async findWithRoles(id: string): Promise<Permission | null> {
    return this.repository.findOne({
      where: { id },
      relations: ["roles"]
    });
  }
}
