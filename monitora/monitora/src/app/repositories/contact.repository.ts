import { Provider } from '@core/decorators/decorators';
import { BaseRepository } from '@core/repositories/base.repository';
import { Contact } from '../../entities/contact.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class ContactRepository extends BaseRepository<Contact> {
  constructor() {
    super(Contact);
  }

  @Mock('ContactRepository-findByEmail')
  async findByEmail(email: string): Promise<Contact | null> {
    return this.repository.findOne({ where: { email } });
  }

  @Mock('ContactRepository-findWithAddresses')
  async findWithAddresses(id: string): Promise<Contact | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['addresses']
    });
  }
}