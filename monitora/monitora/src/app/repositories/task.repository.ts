import { Provider } from '@core/decorators/decorators';
import { BaseRepository } from '@core/repositories/base.repository';
import { Task } from '../../entities/task.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class TaskRepository extends BaseRepository<Task> {
  constructor() {
    super(Task);
  }

  @Mock('TaskRepository-findByAssignee')
  async findByAssignee(userId: string): Promise<Task[]> {
    return this.repository.find({
      where: { assignedTo: { id: userId } },
      relations: ['assignedTo']
    });
  }

  @Mock('TaskRepository-findWithRelations')
  async findWithRelations(id: string): Promise<Task | null> {
    return this.repository.findOne({
      where: { id },
      relations: ['assignedTo', 'createdBy']
    });
  }
}
