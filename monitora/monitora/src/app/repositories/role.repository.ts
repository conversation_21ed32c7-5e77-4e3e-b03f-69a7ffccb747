import { Provider } from "@core/decorators/decorators";
import { BaseRepository } from "@core/repositories/base.repository";
import { Role } from "../../entities/role.entity";

@Provider()
export class RoleRepository extends BaseRepository<Role> {
  constructor() {
    super(Role);
  }

  async findByName(name: string): Promise<Role | null> {
    return this.repository.findOne({ where: { name } });
  }

  async findWithPermissions(id: string): Promise<Role | null> {
    return this.repository.findOne({
      where: { id },
      relations: ["permissions"]
    });
  }

  async findWithUsers(id: string): Promise<Role | null> {
    return this.repository.findOne({
      where: { id },
      relations: ["users"]
    });
  }

  async findAllActive(): Promise<Role[]> {
    return this.repository.find({
      where: { isActive: true },
      relations: ["permissions"]
    });
  }

  async findSystemRoles(): Promise<Role[]> {
    return this.repository.find({
      where: { isSystemRole: true },
      relations: ["permissions"]
    });
  }
}
