import { Provider } from '@core/decorators/decorators';
import { BaseRepository } from '@core/repositories/base.repository';
import { DomainMonitor } from '../../entities/domain-monitor.entity';
import { Brand } from '../../entities/brand.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class DomainMonitorRepository extends BaseRepository<DomainMonitor> {
  constructor() {
    super(DomainMonitor);
  }

  @Mock('DomainMonitorRepository-findByDomain')
  async findByDomain(domain: string): Promise<DomainMonitor | null> {
    return this.repository.findOne({ 
      where: { domain },
      relations: ['brand', 'threatDetections']
    });
  }

  @Mock('DomainMonitorRepository-findByBrand')
  async findByBrand(brandId: string): Promise<DomainMonitor[]> {
    return this.repository.find({ 
      where: { brandId },
      relations: ['threatDetections'],
      order: { createdAt: 'DESC' }
    });
  }

  @Mock('DomainMonitorRepository-findByStatus')
  async findByStatus(status: DomainMonitor['status']): Promise<DomainMonitor[]> {
    return this.repository.find({ 
      where: { status },
      relations: ['brand'],
      order: { firstDetected: 'DESC' }
    });
  }

  @Mock('DomainMonitorRepository-findSuspiciousDomains')
  async findSuspiciousDomains(brandId?: string): Promise<DomainMonitor[]> {
    const query = this.repository
      .createQueryBuilder('domain')
      .leftJoinAndSelect('domain.brand', 'brand')
      .where('domain.status IN (:...statuses)', { 
        statuses: ['suspicious', 'confirmed_threat'] 
      })
      .andWhere('domain.isActive = :isActive', { isActive: true });

    if (brandId) {
      query.andWhere('domain.brandId = :brandId', { brandId });
    }

    return query
      .orderBy('domain.similarityScore', 'DESC')
      .addOrderBy('domain.firstDetected', 'DESC')
      .getMany();
  }

  @Mock('DomainMonitorRepository-findHighSimilarityDomains')
  async findHighSimilarityDomains(threshold: number = 0.8): Promise<DomainMonitor[]> {
    return this.repository
      .createQueryBuilder('domain')
      .leftJoinAndSelect('domain.brand', 'brand')
      .where('domain.similarityScore >= :threshold', { threshold })
      .andWhere('domain.isActive = :isActive', { isActive: true })
      .orderBy('domain.similarityScore', 'DESC')
      .getMany();
  }

  @Mock('DomainMonitorRepository-findDomainsNeedingRecheck')
  async findDomainsNeedingRecheck(hoursAgo: number = 24): Promise<DomainMonitor[]> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hoursAgo);

    return this.repository
      .createQueryBuilder('domain')
      .where('domain.isActive = :isActive', { isActive: true })
      .andWhere('(domain.lastChecked IS NULL OR domain.lastChecked < :cutoffDate)', { cutoffDate })
      .orderBy('domain.lastChecked', 'ASC')
      .getMany();
  }

  @Mock('DomainMonitorRepository-getBrandForDomain')
  async getBrandForDomain(domainId: string): Promise<Brand | null> {
    const domain = await this.repository.findOne({
      where: { id: domainId },
      relations: ['brand']
    });
    
    return domain?.brand || null;
  }

  @Mock('DomainMonitorRepository-updateBulkStatus')
  async updateBulkStatus(domainIds: string[], status: DomainMonitor['status']): Promise<void> {
    await this.repository
      .createQueryBuilder()
      .update(DomainMonitor)
      .set({ status, lastChecked: new Date() })
      .where('id IN (:...ids)', { ids: domainIds })
      .execute();
  }

  @Mock('DomainMonitorRepository-getStatsByBrand')
  async getStatsByBrand(brandId: string): Promise<{
    total: number;
    byStatus: { [key: string]: number };
    byDetectionType: { [key: string]: number };
    averageSimilarity: number;
  }> {
    const domains = await this.findByBrand(brandId);
    
    const stats = {
      total: domains.length,
      byStatus: {} as { [key: string]: number },
      byDetectionType: {} as { [key: string]: number },
      averageSimilarity: 0
    };

    domains.forEach(domain => {
      // Count by status
      stats.byStatus[domain.status] = (stats.byStatus[domain.status] || 0) + 1;
      
      // Count by detection type
      stats.byDetectionType[domain.detectionType] = (stats.byDetectionType[domain.detectionType] || 0) + 1;
    });

    // Calculate average similarity
    if (domains.length > 0) {
      const totalSimilarity = domains.reduce((sum, domain) => sum + domain.similarityScore, 0);
      stats.averageSimilarity = totalSimilarity / domains.length;
    }

    return stats;
  }

  @Mock('DomainMonitorRepository-searchDomains')
  async searchDomains(query: string, brandId?: string): Promise<DomainMonitor[]> {
    const queryBuilder = this.repository
      .createQueryBuilder('domain')
      .leftJoinAndSelect('domain.brand', 'brand')
      .where('domain.domain ILIKE :query', { query: `%${query}%` });

    if (brandId) {
      queryBuilder.andWhere('domain.brandId = :brandId', { brandId });
    }

    return queryBuilder
      .orderBy('domain.similarityScore', 'DESC')
      .getMany();
  }
}
