import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { LeadService } from '../services/lead.service';

@Controller('/leads')
export class LeadController {
  constructor(private leadService: LeadService) {}

  @Get('/')
  @Middleware(auth)
  async getAll(req: Request, res: Response) {
    const leads = await this.leadService.findAll();
    res.json(leads);
  }

  @Post('/qualify')
  @Middleware(auth)
  async qualifyLead(req: Request, res: Response) {
    const lead = await this.leadService.qualifyLead(req.params.id, req.body);
    res.json(lead);
  }

  @Post('/convert')
  @Middleware(auth)
  async convertToOpportunity(req: Request, res: Response) {
    const opportunity = await this.leadService.convertToOpportunity(req.params.id);
    res.json(opportunity);
  }
}