import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { CustomFieldService } from '../services/custom-field.service';

@Controller('/custom-fields')
export class CustomFieldController {
  constructor(private customFieldService: CustomFieldService) {}

  @Post('/:entityType')
  @Middleware(auth)
  async createField(req: Request, res: Response) {
    const field = await this.customFieldService.create(
      req.params.entityType,
      req.body
    );
    res.status(201).json(field);
  }

  @Get('/:entityType')
  @Middleware(auth)
  async getFields(req: Request, res: Response) {
    const fields = await this.customFieldService.findByEntity(
      req.params.entityType
    );
    res.json(fields);
  }
}