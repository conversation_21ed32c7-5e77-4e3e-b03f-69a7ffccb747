import { Controller, Get } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { HealthService } from '../services/health.service';

@Controller('/health')
export class HealthController {
  constructor(private healthService: HealthService) {}

  @Get('/')
  async check(req: Request, res: Response) {
    const status = await this.healthService.checkAll();
    res.status(status.isHealthy ? 200 : 503).json(status);
  }

  @Get('/db')
  async checkDatabase(req: Request, res: Response) {
    const dbStatus = await this.healthService.checkDatabase();
    res.status(dbStatus.isHealthy ? 200 : 503).json(dbStatus);
  }
}