import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { OpportunityService } from '../services/opportunity.service';

@Controller('/opportunities')
export class OpportunityController {
  constructor(private opportunityService: OpportunityService) {}

  @Get('/')
  @Middleware(auth)
  async getAll(req: Request, res: Response) {
    const opportunities = await this.opportunityService.findAll();
    res.json(opportunities);
  }

  @Get('/pipeline')
  @Middleware(auth)
  async getPipeline(req: Request, res: Response) {
    const pipeline = await this.opportunityService.getPipelineView();
    res.json(pipeline);
  }

  @Get('/:id')
  @Middleware(auth)
  async getOne(req: Request, res: Response) {
    const opportunity = await this.opportunityService.findById(req.params.id);
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    res.json(opportunity);
  }

  @Post('/')
  @Middleware(auth)
  async create(req: Request, res: Response) {
    const opportunity = await this.opportunityService.create({
      ...req.body,
      owner: req.currentUser
    });
    res.status(201).json(opportunity);
  }

  @Put('/:id')
  @Middleware(auth)
  async update(req: Request, res: Response) {
    const opportunity = await this.opportunityService.update(req.params.id, req.body);
    if (!opportunity) {
      return res.status(404).json({ message: 'Opportunity not found' });
    }
    res.json(opportunity);
  }

  @Delete('/:id')
  @Middleware(auth)
  async delete(req: Request, res: Response) {
    await this.opportunityService.delete(req.params.id);
    res.status(204).send();
  }
}