import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { CampaignService } from '../services/campaign.service';

@Controller('/campaigns')
export class CampaignController {
  constructor(private campaignService: CampaignService) {}

  @Get('/')
  @Middleware(auth)
  async getAll(req: Request, res: Response) {
    const campaigns = await this.campaignService.findAll();
    res.json(campaigns);
  }

  @Post('/send')
  @Middleware(auth)
  async sendCampaign(req: Request, res: Response) {
    await this.campaignService.sendCampaign(req.params.id);
    res.status(200).json({ message: 'Campaign scheduled for sending' });
  }

  @Get('/:id/metrics')
  @Middleware(auth)
  async getCampaignMetrics(req: Request, res: Response) {
    const metrics = await this.campaignService.getMetrics(req.params.id);
    res.json(metrics);
  }
}