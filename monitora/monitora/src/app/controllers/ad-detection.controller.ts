import { Controller, Get, Post, Put, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { AdDetectionService } from '../services/ad-detection.service';
import { BrandService } from '../services/brand.service';
import { auth } from '../middlewares/auth.middleware';

@Controller('/ad-detection')
export class AdDetectionController {
  constructor(
    private adDetectionService: AdDetectionService,
    private brandService: BrandService
  ) {}

  @Post('/scan/:brandId')
  @Middleware(auth)
  async scanForScamAds(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const scamAds = await this.adDetectionService.scanForScamAds(brand);
      
      res.json({
        message: 'Ad scan completed',
        found: scamAds.length,
        scamAds: scamAds.map(ad => ({
          platform: ad.platform,
          adId: ad.adId,
          content: ad.content.substring(0, 200) + (ad.content.length > 200 ? '...' : ''),
          targetUrl: ad.targetUrl,
          riskScore: ad.riskScore,
          scamIndicators: ad.scamIndicators,
          isScam: ad.isScam
        }))
      });
    } catch (error) {
      res.status(500).json({ 
        message: 'Error scanning for scam ads', 
        error: error.message 
      });
    }
  }

  @Get('/scan/:brandId/detailed/:adId')
  @Middleware(auth)
  async getDetailedAdAnalysis(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      // This would typically fetch from a database where we store scan results
      // For now, we'll return a placeholder response
      res.json({
        message: 'Detailed ad analysis not yet implemented',
        adId: req.params.adId,
        brandId: req.params.brandId
      });
    } catch (error) {
      res.status(500).json({ 
        message: 'Error fetching detailed ad analysis', 
        error: error.message 
      });
    }
  }

  @Post('/report')
  @Middleware(auth)
  async reportScamAd(req: Request, res: Response) {
    try {
      const { platform, adId, reason, brandId } = req.body;

      if (!platform || !adId || !reason || !brandId) {
        return res.status(400).json({ 
          message: 'Platform, adId, reason, and brandId are required' 
        });
      }

      // Verify brand ownership
      const brand = await this.brandService.findById(brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const success = await this.adDetectionService.reportScamAd(platform, adId, reason);
      
      if (success) {
        res.json({ 
          message: 'Ad reported successfully',
          platform,
          adId,
          reason
        });
      } else {
        res.status(500).json({ 
          message: 'Failed to report ad',
          platform,
          adId
        });
      }
    } catch (error) {
      res.status(500).json({ 
        message: 'Error reporting scam ad', 
        error: error.message 
      });
    }
  }

  @Get('/guide')
  @Middleware(auth)
  async getScamDetectionGuide(req: Request, res: Response) {
    try {
      const guide = {
        title: 'How to Identify Scam Ads',
        indicators: [
          {
            category: 'Brand Impersonation',
            signs: [
              'Uses your brand name without authorization',
              'Similar logos or visual elements',
              'Claims to be official when they are not',
              'Fake verification badges or claims'
            ]
          },
          {
            category: 'Suspicious URLs',
            signs: [
              'Domain contains your brand name but isn\'t your official domain',
              'Misspelled versions of your domain',
              'Suspicious TLDs (.tk, .ml, .ga)',
              'URL shorteners hiding the real destination'
            ]
          },
          {
            category: 'Scam Content',
            signs: [
              'Too-good-to-be-true offers (90%+ discounts)',
              'Fake giveaways or contests',
              'Urgency tactics ("Limited time", "Act now")',
              'Requests for personal information'
            ]
          },
          {
            category: 'Technical Red Flags',
            signs: [
              'Very short ad campaign duration',
              'Low ad spend (test campaigns)',
              'Unverified advertiser accounts',
              'Poor quality images or text'
            ]
          }
        ],
        actionSteps: [
          'Document the ad with screenshots',
          'Note the advertiser information',
          'Check the target URL carefully',
          'Report through platform-specific channels',
          'Monitor for similar ads'
        ],
        reportingChannels: {
          facebook: 'https://www.facebook.com/help/contact/****************',
          instagram: 'https://help.instagram.com/contact/***************',
          google: 'https://support.google.com/google-ads/contact/vio_other_aw_policy',
          twitter: 'https://help.twitter.com/en/forms/ads'
        }
      };

      res.json(guide);
    } catch (error) {
      res.status(500).json({ 
        message: 'Error fetching scam detection guide', 
        error: error.message 
      });
    }
  }

  @Get('/stats/:brandId')
  @Middleware(auth)
  async getAdDetectionStats(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand) {
        return res.status(404).json({ message: 'Brand not found' });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      // This would typically come from stored scan results
      // For now, return placeholder stats
      const stats = {
        totalScansPerformed: 0,
        scamAdsDetected: 0,
        adsReported: 0,
        platformBreakdown: {
          facebook: { scanned: 0, detected: 0 },
          instagram: { scanned: 0, detected: 0 },
          google: { scanned: 0, detected: 0 },
          twitter: { scanned: 0, detected: 0 }
        },
        riskLevelBreakdown: {
          low: 0,
          medium: 0,
          high: 0,
          critical: 0
        },
        lastScanDate: null,
        nextScheduledScan: null
      };

      res.json(stats);
    } catch (error) {
      res.status(500).json({ 
        message: 'Error fetching ad detection stats', 
        error: error.message 
      });
    }
  }
}
