import { Controller, Get, Post, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { ActivityService } from '../services/activity.service';

@Controller('/activities')
export class ActivityController {
  constructor(private activityService: ActivityService) {}

  @Get('/:entityType/:entityId')
  @Middleware(auth)
  async getActivities(req: Request, res: Response) {
    const activities = await this.activityService.findByEntity(
      req.params.entityType,
      req.params.entityId
    );
    res.json(activities);
  }

  @Post('/log')
  @Middleware(auth)
  async logActivity(req: Request, res: Response) {
    const activity = await this.activityService.create({
      ...req.body,
      userId: req.currentUser.id
    });
    res.status(201).json(activity);
  }
}