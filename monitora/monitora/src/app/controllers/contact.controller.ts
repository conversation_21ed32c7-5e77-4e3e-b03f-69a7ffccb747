import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { ContactService } from '../services/contact.service';

@Controller('/contacts')
export class ContactController {
  constructor(private contactService: ContactService) {}

  @Get('/')
  @Middleware(auth)
  async getAll(req: Request, res: Response) {
    const contacts = await this.contactService.findAll();
    res.json(contacts);
  }

  @Get('/:id')
  @Middleware(auth)
  async getOne(req: Request, res: Response) {
    const contact = await this.contactService.findById(req.params.id);
    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }
    res.json(contact);
  }

  @Post('/')
  @Middleware(auth)
  async create(req: Request, res: Response) {
    const contact = await this.contactService.create(req.body);
    res.status(201).json(contact);
  }

  @Put('/:id')
  @Middleware(auth)
  async update(req: Request, res: Response) {
    const contact = await this.contactService.update(req.params.id, req.body);
    if (!contact) {
      return res.status(404).json({ message: 'Contact not found' });
    }
    res.json(contact);
  }

  @Delete('/:id')
  @Middleware(auth)
  async delete(req: Request, res: Response) {
    await this.contactService.delete(req.params.id);
    res.status(204).send();
  }
}