import { Controller, Get, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { AuditService } from '../services/audit.service';

@Controller('/audit')
export class AuditController {
  constructor(private auditService: AuditService) {}

  @Get('/logs')
  @Middleware(auth)
  async getLogs(req: Request, res: Response) {
    const logs = await this.auditService.getLogs({
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      entityType: req.query.entityType as string,
      userId: req.query.userId as string
    });
    res.json(logs);
  }

  @Get('/changes/:entityId')
  @Middleware(auth)
  async getEntityChanges(req: Request, res: Response) {
    const changes = await this.auditService.getEntityChanges(req.params.entityId);
    res.json(changes);
  }
}