import { Controller, Get, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { SearchService } from '../../app/services/search.service';

@Controller('/search')
export class SearchController {
  constructor(private searchService: SearchService) {}

  @Get('/')
  @Middleware(auth)
  async search(req: Request, res: Response) {
    const results = await this.searchService.search({
      query: req.query.q as string,
      types: (req.query.types as string || '').split(','),
      page: parseInt(req.query.page as string) || 1,
      limit: parseInt(req.query.limit as string) || 20
    });
    res.json(results);
  }

  @Get('/suggest')
  @Middleware(auth)
  async suggest(req: Request, res: Response) {
    const suggestions = await this.searchService.suggest(req.query.q as string);
    res.json(suggestions);
  }
}