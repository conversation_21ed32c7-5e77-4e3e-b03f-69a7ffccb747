import { Controller, Get, Post, Put, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { ThreatDetectionService } from '../services/threat-detection.service';
import { DomainMonitoringService } from '../services/domain-monitoring.service';
import { SocialMediaMonitoringService } from '../services/social-media-monitoring.service';
import { AdDetectionService } from '../services/ad-detection.service';
import { AutomatedResponseService } from '../services/automated-response.service';
import { BrandService } from '../services/brand.service';
import { auth } from '../middlewares/auth.middleware';

@Controller('/findings')
export class FindingsController {
  constructor(
    private threatDetectionService: ThreatDetectionService,
    private domainMonitoringService: DomainMonitoringService,
    private socialMediaMonitoringService: SocialMediaMonitoringService,
    private adDetectionService: AdDetectionService,
    private automatedResponseService: AutomatedResponseService,
    private brandService: BrandService
  ) {}

  @Get('/all/:brandId')
  @Middleware(auth)
  async getAllFindings(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const { type, severity, status, limit = 50, offset = 0 } = req.query;

      const [
        domains,
        socialMedia,
        threats,
        scamAds
      ] = await Promise.all([
        this.domainMonitoringService.getDomainMonitors(req.params.brandId),
        this.socialMediaMonitoringService.getSocialMediaMonitors(req.params.brandId),
        this.threatDetectionService.getThreatsForBrand(req.params.brandId),
        this.adDetectionService.scanForScamAds(brand)
      ]);

      let findings = [];

      // Add domain findings
      if (!type || type === 'domain') {
        findings.push(...domains.map(d => ({
          id: d.id,
          type: 'domain',
          title: `Suspicious domain: ${d.domain}`,
          description: `Domain similarity: ${(d.similarityScore * 100).toFixed(1)}%`,
          severity: this.mapDomainSeverity(d),
          status: d.status,
          url: `https://${d.domain}`,
          detectedAt: d.firstDetected,
          lastChecked: d.lastChecked,
          riskFactors: d.riskFactors,
          evidence: {
            whoisData: d.whoisData,
            websiteData: d.websiteData,
            similarityScore: d.similarityScore
          },
          actions: {
            canReport: true,
            canTakedown: d.status === 'confirmed_threat',
            canBlock: true
          }
        })));
      }

      // Add social media findings
      if (!type || type === 'social_media') {
        findings.push(...socialMedia.map(s => ({
          id: s.id,
          type: 'social_media',
          title: `${s.platform} ${s.contentType}: ${s.authorDisplayName || s.authorUsername}`,
          description: s.content.substring(0, 100) + (s.content.length > 100 ? '...' : ''),
          severity: this.mapSocialMediaSeverity(s),
          status: s.status,
          url: s.contentUrl,
          detectedAt: s.firstDetected,
          lastChecked: s.lastChecked,
          platform: s.platform,
          contentType: s.contentType,
          engagement: s.engagement,
          riskFactors: s.riskFactors,
          evidence: {
            content: s.content,
            authorProfile: s.authorProfile,
            mediaUrls: s.mediaUrls,
            hashtags: s.hashtags,
            mentions: s.mentions
          },
          actions: {
            canReport: true,
            canTakedown: s.status === 'confirmed_threat',
            canBlock: false
          }
        })));
      }

      // Add ad findings
      if (!type || type === 'ad') {
        findings.push(...scamAds.map(ad => ({
          id: ad.adId,
          type: 'ad',
          title: `Scam ad on ${ad.platform}`,
          description: ad.content.substring(0, 100) + (ad.content.length > 100 ? '...' : ''),
          severity: this.mapAdSeverity(ad.riskScore),
          status: ad.isScam ? 'confirmed_threat' : 'suspicious',
          url: ad.targetUrl,
          detectedAt: new Date(),
          platform: ad.platform,
          riskScore: ad.riskScore,
          scamIndicators: ad.scamIndicators,
          evidence: ad.evidence,
          actions: {
            canReport: true,
            canTakedown: ad.isScam,
            canBlock: false
          }
        })));
      }

      // Add general threats
      if (!type || type === 'threat') {
        findings.push(...threats.map(t => ({
          id: t.id,
          type: 'threat',
          title: `${t.threatType}: ${t.description}`,
          description: t.description,
          severity: t.severity,
          status: t.status,
          url: t.sourceUrl,
          detectedAt: t.firstDetected,
          lastUpdated: t.lastUpdated,
          sourceType: t.sourceType,
          confidenceScore: t.confidenceScore,
          riskAssessment: t.riskAssessment,
          evidence: t.evidence,
          mitigationSteps: t.mitigationSteps,
          actions: {
            canReport: true,
            canTakedown: t.status === 'confirmed',
            canEscalate: t.status !== 'escalated'
          }
        })));
      }

      // Apply filters
      if (severity) {
        findings = findings.filter(f => f.severity === severity);
      }
      if (status) {
        findings = findings.filter(f => f.status === status);
      }

      // Sort by severity and detection date
      findings.sort((a, b) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const severityDiff = (severityOrder[b.severity] || 0) - (severityOrder[a.severity] || 0);
        if (severityDiff !== 0) return severityDiff;
        return new Date(b.detectedAt).getTime() - new Date(a.detectedAt).getTime();
      });

      // Apply pagination
      const paginatedFindings = findings.slice(Number(offset), Number(offset) + Number(limit));

      res.json({
        findings: paginatedFindings,
        total: findings.length,
        summary: {
          byType: this.groupByType(findings),
          bySeverity: this.groupBySeverity(findings),
          byStatus: this.groupByStatus(findings)
        },
        pagination: {
          limit: Number(limit),
          offset: Number(offset),
          hasMore: findings.length > Number(offset) + Number(limit)
        }
      });
    } catch (error) {
      res.status(500).json({ message: 'Error fetching findings', error: error.message });
    }
  }

  @Get('/domains/:brandId')
  @Middleware(auth)
  async getDomainFindings(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const domains = await this.domainMonitoringService.getDomainMonitors(req.params.brandId);
      
      res.json({
        type: 'domain',
        count: domains.length,
        findings: domains.map(d => ({
          id: d.id,
          domain: d.domain,
          status: d.status,
          similarityScore: d.similarityScore,
          detectionType: d.detectionType,
          riskFactors: d.riskFactors,
          firstDetected: d.firstDetected,
          lastChecked: d.lastChecked,
          whoisData: d.whoisData,
          websiteData: d.websiteData
        }))
      });
    } catch (error) {
      res.status(500).json({ message: 'Error fetching domain findings', error: error.message });
    }
  }

  @Get('/social-media/:brandId')
  @Middleware(auth)
  async getSocialMediaFindings(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const { platform } = req.query;
      let socialMedia = await this.socialMediaMonitoringService.getSocialMediaMonitors(req.params.brandId);
      
      if (platform) {
        socialMedia = socialMedia.filter(s => s.platform === platform);
      }

      res.json({
        type: 'social_media',
        count: socialMedia.length,
        findings: socialMedia.map(s => ({
          id: s.id,
          platform: s.platform,
          contentType: s.contentType,
          contentUrl: s.contentUrl,
          content: s.content,
          authorUsername: s.authorUsername,
          authorDisplayName: s.authorDisplayName,
          status: s.status,
          similarityScore: s.similarityScore,
          detectionType: s.detectionType,
          riskFactors: s.riskFactors,
          engagement: s.engagement,
          firstDetected: s.firstDetected,
          publishedAt: s.publishedAt
        }))
      });
    } catch (error) {
      res.status(500).json({ message: 'Error fetching social media findings', error: error.message });
    }
  }

  @Get('/ads/:brandId')
  @Middleware(auth)
  async getAdFindings(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const scamAds = await this.adDetectionService.scanForScamAds(brand);
      
      res.json({
        type: 'ad',
        count: scamAds.length,
        findings: scamAds.map(ad => ({
          adId: ad.adId,
          platform: ad.platform,
          content: ad.content,
          targetUrl: ad.targetUrl,
          isScam: ad.isScam,
          riskScore: ad.riskScore,
          scamIndicators: ad.scamIndicators,
          evidence: ad.evidence
        }))
      });
    } catch (error) {
      res.status(500).json({ message: 'Error fetching ad findings', error: error.message });
    }
  }

  @Get('/threats/:brandId')
  @Middleware(auth)
  async getThreatFindings(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      const { threatType, sourceType } = req.query;
      const filters: any = {};
      if (threatType) filters.threatType = threatType;
      if (sourceType) filters.sourceType = sourceType;

      const threats = await this.threatDetectionService.getThreatsForBrand(req.params.brandId, filters);
      
      res.json({
        type: 'threat',
        count: threats.length,
        findings: threats
      });
    } catch (error) {
      res.status(500).json({ message: 'Error fetching threat findings', error: error.message });
    }
  }

  @Post('/bulk-action')
  @Middleware(auth)
  async performBulkAction(req: Request, res: Response) {
    try {
      const { action, findingIds, findingType, brandId } = req.body;

      // Verify brand ownership
      const brand = await this.brandService.findById(brandId);
      if (!brand || brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: 'Access denied' });
      }

      let results = [];

      switch (action) {
        case 'report':
          results = await this.performBulkReport(findingIds, findingType);
          break;
        case 'mark_false_positive':
          results = await this.performBulkMarkFalsePositive(findingIds, findingType);
          break;
        case 'escalate':
          results = await this.performBulkEscalate(findingIds, findingType);
          break;
        default:
          return res.status(400).json({ message: 'Invalid action' });
      }

      res.json({
        action,
        processed: results.length,
        results
      });
    } catch (error) {
      res.status(500).json({ message: 'Error performing bulk action', error: error.message });
    }
  }

  private mapDomainSeverity(domain: any): string {
    if (domain.similarityScore > 0.9) return 'critical';
    if (domain.similarityScore > 0.8) return 'high';
    if (domain.similarityScore > 0.7) return 'medium';
    return 'low';
  }

  private mapSocialMediaSeverity(socialMedia: any): string {
    const riskCount = socialMedia.riskFactors?.length || 0;
    if (riskCount >= 3) return 'critical';
    if (riskCount >= 2) return 'high';
    if (riskCount >= 1) return 'medium';
    return 'low';
  }

  private mapAdSeverity(riskScore: number): string {
    if (riskScore >= 90) return 'critical';
    if (riskScore >= 70) return 'high';
    if (riskScore >= 50) return 'medium';
    return 'low';
  }

  private groupByType(findings: any[]): { [key: string]: number } {
    return findings.reduce((acc, f) => {
      acc[f.type] = (acc[f.type] || 0) + 1;
      return acc;
    }, {});
  }

  private groupBySeverity(findings: any[]): { [key: string]: number } {
    return findings.reduce((acc, f) => {
      acc[f.severity] = (acc[f.severity] || 0) + 1;
      return acc;
    }, {});
  }

  private groupByStatus(findings: any[]): { [key: string]: number } {
    return findings.reduce((acc, f) => {
      acc[f.status] = (acc[f.status] || 0) + 1;
      return acc;
    }, {});
  }

  private async performBulkReport(findingIds: string[], findingType: string): Promise<any[]> {
    // Implementation for bulk reporting
    return findingIds.map(id => ({ id, action: 'report', success: true }));
  }

  private async performBulkMarkFalsePositive(findingIds: string[], findingType: string): Promise<any[]> {
    // Implementation for bulk false positive marking
    return findingIds.map(id => ({ id, action: 'mark_false_positive', success: true }));
  }

  private async performBulkEscalate(findingIds: string[], findingType: string): Promise<any[]> {
    // Implementation for bulk escalation
    return findingIds.map(id => ({ id, action: 'escalate', success: true }));
  }
}
