import { Controller, Get, Post, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { DocumentService } from '../services/document.service';

@Controller('/documents')
export class DocumentController {
  constructor(private documentService: DocumentService) {}

  @Post('/upload')
  @Middleware(auth)
  async uploadDocument(req: Request, res: Response) {
    const document = await this.documentService.upload(req.files.document, {
      entityType: req.body.entityType,
      entityId: req.body.entityId,
      userId: req.currentUser.id
    });
    res.status(201).json(document);
  }

  @Get('/:entityType/:entityId')
  @Middleware(auth)
  async getDocuments(req: Request, res: Response) {
    const documents = await this.documentService.findByEntity(
      req.params.entityType,
      req.params.entityId
    );
    res.json(documents);
  }
}