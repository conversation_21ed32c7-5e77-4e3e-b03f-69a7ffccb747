import { Controller, Get } from '../../core/decorators/decorators';
import { TodoGateway } from '../gateways/todo.gateway';
import { Request, Response } from 'express';

@Controller('/todo')
export class TodoController {
  constructor(private todoGateway: TodoGateway) {}

  @Get('/:id')
  async getTodo(req: Request, res: Response) {
    const result = await this.todoGateway.getById(req.params.id);

    res.status(200).json(result);
  }
}