import { Controller, Get, Post, Put, Delete, Param, Body, Query } from "@core/decorators/decorators";
import { Request, Response } from "express";
import { GoogleAdsService } from "../services/google-ads.service";
import { RBACService } from "../services/rbac.service";

@Controller("/api/google-ads")
export class GoogleAdsController {
  constructor(
    private googleAdsService: GoogleAdsService,
    private rbacService: RBACService
  ) {}

  @Get("/brands/:brandId/ads")
  async searchBrandAds(req: Request, res: Response) {
    try {
      // Check permission
      const userId = req.user?.id;
      if (!userId || !(await this.rbacService.checkPermission(userId, "threats", "read"))) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const { brandId } = req.params;
      const ads = await this.googleAdsService.searchBrandAds(brandId);

      res.json({
        success: true,
        data: ads,
        total: ads.length
      });
    } catch (error) {
      console.error("Error searching brand ads:", error);
      res.status(500).json({
        success: false,
        error: "Failed to search brand ads"
      });
    }
  }

  @Get("/monitor/all")
  async monitorAllBrands(req: Request, res: Response) {
    try {
      // Check permission - only admins can monitor all brands
      const userId = req.user?.id;
      if (!userId || !(await this.rbacService.checkPermission(userId, "brands", "manage"))) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const reports = await this.googleAdsService.monitorAllBrands();

      res.json({
        success: true,
        data: reports,
        summary: {
          totalBrands: reports.length,
          totalSuspiciousAds: reports.reduce((sum, r) => sum + r.suspiciousAds.length, 0),
          totalHighRiskAds: reports.reduce((sum, r) => sum + r.highRiskAds.length, 0),
          totalEstimatedLoss: reports.reduce((sum, r) => sum + r.estimatedLoss, 0)
        }
      });
    } catch (error) {
      console.error("Error monitoring all brands:", error);
      res.status(500).json({
        success: false,
        error: "Failed to monitor brands"
      });
    }
  }

  @Post("/ads/:adId/report")
  async reportInfringingAd(req: Request, res: Response) {
    try {
      // Check permission
      const userId = req.user?.id;
      if (!userId || !(await this.rbacService.checkPermission(userId, "threats", "update"))) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const { adId } = req.params;
      const { reason } = req.body;

      if (!reason) {
        return res.status(400).json({
          success: false,
          error: "Reason is required"
        });
      }

      const success = await this.googleAdsService.reportInfringingAd(adId, reason);

      if (success) {
        res.json({
          success: true,
          message: "Ad reported successfully"
        });
      } else {
        res.status(500).json({
          success: false,
          error: "Failed to report ad"
        });
      }
    } catch (error) {
      console.error("Error reporting ad:", error);
      res.status(500).json({
        success: false,
        error: "Failed to report ad"
      });
    }
  }

  @Get("/brands/:brandId/metrics")
  async getAdMetrics(req: Request, res: Response) {
    try {
      // Check permission
      const userId = req.user?.id;
      if (!userId || !(await this.rbacService.checkPermission(userId, "reports", "read"))) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      const { brandId } = req.params;
      const { startDate, endDate } = req.query;

      const dateRange = {
        start: startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: endDate ? new Date(endDate as string) : new Date()
      };

      const metrics = await this.googleAdsService.getAdPerformanceMetrics(brandId, dateRange);

      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      console.error("Error getting ad metrics:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get ad metrics"
      });
    }
  }

  @Get("/dashboard")
  async getDashboard(req: Request, res: Response) {
    try {
      // Check permission
      const userId = req.user?.id;
      if (!userId || !(await this.rbacService.checkPermission(userId, "reports", "read"))) {
        return res.status(403).json({ error: "Insufficient permissions" });
      }

      // Get recent monitoring data
      const reports = await this.googleAdsService.monitorAllBrands();
      
      const dashboard = {
        overview: {
          totalBrandsMonitored: reports.length,
          totalAdsFound: reports.reduce((sum, r) => sum + r.totalAdsFound, 0),
          suspiciousAdsCount: reports.reduce((sum, r) => sum + r.suspiciousAds.length, 0),
          highRiskAdsCount: reports.reduce((sum, r) => sum + r.highRiskAds.length, 0),
          totalEstimatedLoss: reports.reduce((sum, r) => sum + r.estimatedLoss, 0)
        },
        topRiskyBrands: reports
          .filter(r => r.highRiskAds.length > 0)
          .sort((a, b) => b.highRiskAds.length - a.highRiskAds.length)
          .slice(0, 5)
          .map(r => ({
            brandName: r.brandName,
            highRiskAdsCount: r.highRiskAds.length,
            estimatedLoss: r.estimatedLoss
          })),
        recentHighRiskAds: reports
          .flatMap(r => r.highRiskAds.map(ad => ({ ...ad, brandName: r.brandName })))
          .sort((a, b) => b.riskScore - a.riskScore)
          .slice(0, 10)
      };

      res.json({
        success: true,
        data: dashboard
      });
    } catch (error) {
      console.error("Error getting dashboard:", error);
      res.status(500).json({
        success: false,
        error: "Failed to get dashboard data"
      });
    }
  }
}
