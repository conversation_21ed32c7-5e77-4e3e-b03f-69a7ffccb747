import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { CalendarService } from '../services/calendar.service';

@Controller('/calendar')
export class CalendarController {
  constructor(private calendarService: CalendarService) {}

  @Post('/events')
  @Middleware(auth)
  async createEvent(req: Request, res: Response) {
    const event = await this.calendarService.createEvent({
      ...req.body,
      organizerId: req.currentUser.id
    });
    res.status(201).json(event);
  }

  @Get('/availability')
  @Middleware(auth)
  async getAvailability(req: Request, res: Response) {
    const slots = await this.calendarService.getAvailableSlots(
      req.query.startDate as string,
      req.query.endDate as string
    );
    res.json(slots);
  }
}