import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { TaskService } from '../services/task.service';

@Controller('/tasks')
export class TaskController {
  constructor(private taskService: TaskService) {}

  @Get('/')
  @Middleware(auth)
  async getAll(req: Request, res: Response) {
    const tasks = await this.taskService.findAll();
    res.json(tasks);
  }

  @Get('/assigned')
  @Middleware(auth)
  async getAssigned(req: Request, res: Response) {
    const tasks = await this.taskService.findByAssignee(req.currentUser.id);
    res.json(tasks);
  }

  @Get('/:id')
  @Middleware(auth)
  async getOne(req: Request, res: Response) {
    const task = await this.taskService.findById(req.params.id);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }
    res.json(task);
  }

  @Post('/')
  @Middleware(auth)
  async create(req: Request, res: Response) {
    const task = await this.taskService.create({
      ...req.body,
      createdBy: req.currentUser
    });
    res.status(201).json(task);
  }

  @Put('/:id')
  @Middleware(auth)
  async update(req: Request, res: Response) {
    const task = await this.taskService.update(req.params.id, req.body);
    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }
    res.json(task);
  }

  @Delete('/:id')
  @Middleware(auth)
  async delete(req: Request, res: Response) {
    await this.taskService.delete(req.params.id);
    res.status(204).send();
  }
}