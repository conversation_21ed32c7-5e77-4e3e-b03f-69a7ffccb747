import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { DashboardService } from '../services/dashboard.service';

@Controller('/dashboards')
export class DashboardController {
  constructor(private dashboardService: DashboardService) {}

  @Post('/')
  @Middleware(auth)
  async createDashboard(req: Request, res: Response) {
    const dashboard = await this.dashboardService.create({
      ...req.body,
      userId: req.currentUser.id
    });
    res.status(201).json(dashboard);
  }

  @Get('/widgets')
  @Middleware(auth)
  async getAvailableWidgets(req: Request, res: Response) {
    const widgets = await this.dashboardService.getAvailableWidgets();
    res.json(widgets);
  }

  @Get('/:id')
  @Middleware(auth)
  async getDashboard(req: Request, res: Response) {
    const dashboard = await this.dashboardService.findById(req.params.id);
    res.json(dashboard);
  }

  @Put('/:id')
  @Middleware(auth)
  async updateDashboard(req: Request, res: Response) {
    const dashboard = await this.dashboardService.update(req.params.id, req.body);
    res.json(dashboard);
  }

  @Delete('/:id')
  @Middleware(auth)
  async deleteDashboard(req: Request, res: Response) {
    await this.dashboardService.delete(req.params.id);
    res.status(204).send();
  }

  @Post('/:id/widgets')
  @Middleware(auth)
  async addWidget(req: Request, res: Response) {
    const widget = await this.dashboardService.addWidget(req.params.id, req.body);
    res.status(201).json(widget);
  }

  @Put('/:id/widgets/:widgetId')
  @Middleware(auth)
  async updateWidget(req: Request, res: Response) {
    const widget = await this.dashboardService.updateWidget(
      req.params.id,
      req.params.widgetId,
      req.body
    );
    res.json(widget);
  }

  @Delete('/:id/widgets/:widgetId')
  @Middleware(auth)
  async removeWidget(req: Request, res: Response) {
    await this.dashboardService.removeWidget(req.params.id, req.params.widgetId);
    res.status(204).send();
  }
}
