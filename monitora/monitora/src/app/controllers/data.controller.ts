import { Controller, Post, Get, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { DataService } from '../services/data.service';

@Controller('/data')
export class DataController {
  constructor(private dataService: DataService) {}

  @Post('/import')
  @Middleware(auth)
  async importData(req: Request, res: Response) {
    const job = await this.dataService.startImport({
      type: req.body.type,
      file: req.files.data,
      mappings: req.body.mappings,
      userId: req.currentUser.id
    });
    res.status(202).json(job);
  }

  @Get('/export/:type')
  @Middleware(auth)
  async exportData(req: Request, res: Response) {
    const stream = await this.dataService.exportData(
      req.params.type,
      req.query
    );
    res.setHeader('Content-Type', 'text/csv');
    stream.pipe(res);
  }
}