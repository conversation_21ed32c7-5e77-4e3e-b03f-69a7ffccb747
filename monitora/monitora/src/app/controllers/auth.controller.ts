import { Controller, Post } from '../../core/decorators/decorators';
import { AuthService } from '../services/auth.service';

@Controller('/auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('/login')
  async login(req: any, res: any) {
    const { username, password } = req.body;
    const result = await this.authService.login(username, password);

    if (!result.success) {
      return res.status(401).json(result);
    }

    res.json(result);
  }
}