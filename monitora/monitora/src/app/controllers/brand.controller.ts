import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Middleware,
} from "@core/decorators/decorators";
import { Request, Response } from "express";
import { BrandService } from "../services/brand.service";
import { auth } from "../middlewares/auth.middleware";

@Controller("/brands")
export class BrandController {
  constructor(private brandService: BrandService) {}

  @Get("/")
  @Middleware(auth)
  async getAll(req: Request, res: Response) {
    try {
      const brands = await this.brandService.findByOwner(req.currentUser.id);
      res.json(brands);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error fetching brands", error: error.message });
    }
  }

  @Get("/:id")
  @Middleware(auth)
  async getOne(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      res.json(brand);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error fetching brand", error: error.message });
    }
  }

  @Post("/")
  @Middleware(auth)
  async create(req: Request, res: Response) {
    try {
      const brandData = {
        ...req.body,
        ownerId: req.currentUser.id,
      };

      const brand = await this.brandService.create(brandData);
      res.status(201).json(brand);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error creating brand", error: error.message });
    }
  }

  @Put("/:id")
  @Middleware(auth)
  async update(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedBrand = await this.brandService.update(
        req.params.id,
        req.body
      );
      res.json(updatedBrand);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error updating brand", error: error.message });
    }
  }

  @Delete("/:id")
  @Middleware(auth)
  async delete(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      await this.brandService.delete(req.params.id);
      res.status(204).send();
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error deleting brand", error: error.message });
    }
  }

  @Post("/:id/keywords")
  @Middleware(auth)
  async addKeyword(req: Request, res: Response) {
    try {
      const { keyword } = req.body;
      if (!keyword) {
        return res.status(400).json({ message: "Keyword is required" });
      }

      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedBrand = await this.brandService.addKeyword(
        req.params.id,
        keyword
      );
      res.json(updatedBrand);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error adding keyword", error: error.message });
    }
  }

  @Delete("/:id/keywords/:keyword")
  @Middleware(auth)
  async removeKeyword(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedBrand = await this.brandService.removeKeyword(
        req.params.id,
        req.params.keyword
      );
      res.json(updatedBrand);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error removing keyword", error: error.message });
    }
  }

  @Put("/:id/monitoring-settings")
  @Middleware(auth)
  async updateMonitoringSettings(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedBrand = await this.brandService.updateMonitoringSettings(
        req.params.id,
        req.body
      );
      res.json(updatedBrand);
    } catch (error) {
      res
        .status(500)
        .json({
          message: "Error updating monitoring settings",
          error: error.message,
        });
    }
  }

  @Get("/:id/stats")
  @Middleware(auth)
  async getStats(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const stats = await this.brandService.getBrandStats(req.params.id);
      res.json(stats);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error fetching brand stats", error: error.message });
    }
  }

  @Post("/:id/domains")
  @Middleware(auth)
  async addDomain(req: Request, res: Response) {
    try {
      const { domain } = req.body;
      if (!domain) {
        return res.status(400).json({ message: "Domain is required" });
      }

      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedBrand = await this.brandService.addDomain(
        req.params.id,
        domain
      );
      res.json(updatedBrand);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error adding domain", error: error.message });
    }
  }

  @Delete("/:id/domains/:domain")
  @Middleware(auth)
  async removeDomain(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const updatedBrand = await this.brandService.removeDomain(
        req.params.id,
        req.params.domain
      );
      res.json(updatedBrand);
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error removing domain", error: error.message });
    }
  }

  @Get("/:id/domains")
  @Middleware(auth)
  async getAllDomains(req: Request, res: Response) {
    try {
      const brand = await this.brandService.findById(req.params.id);
      if (!brand) {
        return res.status(404).json({ message: "Brand not found" });
      }

      // Check ownership
      if (brand.ownerId !== req.currentUser.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const domains = await this.brandService.getAllDomains(req.params.id);
      res.json({ domains });
    } catch (error) {
      res
        .status(500)
        .json({ message: "Error fetching domains", error: error.message });
    }
  }
}
