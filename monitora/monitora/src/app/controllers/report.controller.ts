import { Controller, Get, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { ReportService } from '../services/report.service';

@Controller('/reports')
export class ReportController {
  constructor(private reportService: ReportService) {}

  @Get('/sales/pipeline')
  @Middleware(auth)
  async getSalesPipeline(req: Request, res: Response) {
    const report = await this.reportService.generateSalesPipeline();
    res.json(report);
  }

  @Get('/leads/conversion')
  @Middleware(auth)
  async getLeadConversion(req: Request, res: Response) {
    const report = await this.reportService.generateLeadConversionReport();
    res.json(report);
  }

  @Get('/activities/summary')
  @Middleware(auth)
  async getActivitySummary(req: Request, res: Response) {
    const report = await this.reportService.generateActivitySummary();
    res.json(report);
  }
}