import { Controller, Get, Post, Put, Delete, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { ProductService } from '../services/product.service';

@Controller('/products')
export class ProductController {
  constructor(private productService: ProductService) {}

  @Get('/')
  @Middleware(auth)
  async getAll(req: Request, res: Response) {
    const products = await this.productService.findAll();
    res.json(products);
  }

  @Get('/:id')
  @Middleware(auth)
  async getOne(req: Request, res: Response) {
    const product = await this.productService.findById(req.params.id);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }
    res.json(product);
  }

  @Post('/')
  @Middleware(auth)
  async create(req: Request, res: Response) {
    const product = await this.productService.create(req.body);
    res.status(201).json(product);
  }

  @Put('/:id')
  @Middleware(auth)
  async update(req: Request, res: Response) {
    const product = await this.productService.update(req.params.id, req.body);
    if (!product) {
      return res.status(404).json({ message: 'Product not found' });
    }
    res.json(product);
  }

  @Delete('/:id')
  @Middleware(auth)
  async delete(req: Request, res: Response) {
    await this.productService.delete(req.params.id);
    res.status(204).send();
  }
}