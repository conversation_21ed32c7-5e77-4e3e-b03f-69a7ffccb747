import { <PERSON>, Post, Put, Middleware } from '@core/decorators/decorators';
import { Request, Response } from 'express';
import { auth } from '../middlewares/auth.middleware';
import { validateRequest } from '../middlewares/validation.middleware';
import { CreateUserDto, UpdateUserDto } from '../dtos/user.dto';
import { UserService } from '../services/user.service';

@Controller('/users')
export class UserController {
  constructor(private userService: UserService) {}

  @Post('/')
  @Middleware(validateRequest(CreateUserDto))
  async createUser(req: Request, res: Response) {
    const user = await this.userService.create(req.body);
    res.status(201).json(user);
  }

  @Put('/:id')
  @Middleware([auth, validateRequest(UpdateUserDto)])
  async updateUser(req: Request, res: Response) {
    const user = await this.userService.update(req.params.id, req.body);
    res.json(user);
  }
}
