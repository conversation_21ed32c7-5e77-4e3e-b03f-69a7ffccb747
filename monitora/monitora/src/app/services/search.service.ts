import { Provider } from "@core/decorators/decorators";
import { <PERSON><PERSON> } from "@core/decorators/mock.decorator";
import { BrandRepository } from "../repositories/brand.repository";
import { DomainMonitorRepository } from "../repositories/domain-monitor.repository";
import { ThreatDetectionRepository } from "../repositories/threat-detection.repository";
import { AlertRepository } from "../repositories/alert.repository";

interface SearchOptions {
  query: string;
  types: string[];
  page: number;
  limit: number;
}

interface SearchResult {
  type: string;
  id: string;
  title: string;
  description: string;
  url?: string;
  metadata?: any;
}

interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

@Provider()
export class SearchService {
  constructor(
    private brandRepository: BrandRepository,
    private domainMonitorRepository: DomainMonitorRepository,
    private threatDetectionRepository: ThreatDetectionRepository,
    private alertRepository: AlertRepository
  ) {}

  @Mock("SearchService-search")
  async search(options: SearchOptions): Promise<SearchResponse> {
    const { query, types, page, limit } = options;
    const offset = (page - 1) * limit;

    let allResults: SearchResult[] = [];

    // Search brands if no types specified or 'brands' is included
    if (types.length === 0 || types.includes("brands")) {
      const brandResults = await this.searchBrands(query, limit);
      allResults.push(...brandResults);
    }

    // Search domain monitors if no types specified or 'domains' is included
    if (types.length === 0 || types.includes("domains")) {
      const domainResults = await this.searchDomains(query, limit);
      allResults.push(...domainResults);
    }

    // Search threats if no types specified or 'threats' is included
    if (types.length === 0 || types.includes("threats")) {
      const threatResults = await this.searchThreats(query, limit);
      allResults.push(...threatResults);
    }

    // Search alerts if no types specified or 'alerts' is included
    if (types.length === 0 || types.includes("alerts")) {
      const alertResults = await this.searchAlerts(query, limit);
      allResults.push(...alertResults);
    }

    // Sort by relevance (simple implementation)
    allResults.sort((a, b) => {
      const aScore = this.calculateRelevanceScore(a, query);
      const bScore = this.calculateRelevanceScore(b, query);
      return bScore - aScore;
    });

    // Apply pagination
    const paginatedResults = allResults.slice(offset, offset + limit);

    return {
      results: paginatedResults,
      total: allResults.length,
      page,
      limit,
      hasMore: offset + limit < allResults.length,
    };
  }

  @Mock("SearchService-suggest")
  async suggest(query: string): Promise<string[]> {
    if (!query || query.length < 2) {
      return [];
    }

    const suggestions: string[] = [];

    // Get brand name suggestions
    const brands = await this.brandRepository.findAll();
    brands.forEach((brand) => {
      if (brand.name.toLowerCase().includes(query.toLowerCase())) {
        suggestions.push(brand.name);
      }
      // Add keywords that match
      if (brand.keywords) {
        brand.keywords.forEach((keyword) => {
          if (
            keyword.toLowerCase().includes(query.toLowerCase()) &&
            !suggestions.includes(keyword)
          ) {
            suggestions.push(keyword);
          }
        });
      }
    });

    // Get domain suggestions
    const domains = await this.domainMonitorRepository.findAll();
    domains.forEach((domain) => {
      if (
        domain.domain.toLowerCase().includes(query.toLowerCase()) &&
        !suggestions.includes(domain.domain)
      ) {
        suggestions.push(domain.domain);
      }
    });

    // Return top 10 suggestions
    return suggestions.slice(0, 10);
  }

  private async searchBrands(
    query: string,
    limit: number
  ): Promise<SearchResult[]> {
    const brands = await this.brandRepository.findAll();
    const results: SearchResult[] = [];

    brands.forEach((brand) => {
      if (
        this.matchesQuery(brand.name, query) ||
        this.matchesQuery(brand.description || "", query) ||
        (brand.keywords &&
          brand.keywords.some((k) => this.matchesQuery(k, query)))
      ) {
        results.push({
          type: "brand",
          id: brand.id,
          title: brand.name,
          description: brand.description || "Brand monitoring",
          metadata: {
            primaryDomain: brand.primaryDomain,
            status: brand.status,
            keywords: brand.keywords,
          },
        });
      }
    });

    return results.slice(0, limit);
  }

  private async searchDomains(
    query: string,
    limit: number
  ): Promise<SearchResult[]> {
    const domains = await this.domainMonitorRepository.findAll();
    const results: SearchResult[] = [];

    domains.forEach((domain) => {
      if (this.matchesQuery(domain.domain, query)) {
        results.push({
          type: "domain",
          id: domain.id,
          title: domain.domain,
          description: `Domain monitor - Status: ${domain.status}`,
          metadata: {
            status: domain.status,
            similarityScore: domain.similarityScore,
            detectionType: domain.detectionType,
            lastChecked: domain.lastChecked,
          },
        });
      }
    });

    return results.slice(0, limit);
  }

  private async searchThreats(
    query: string,
    limit: number
  ): Promise<SearchResult[]> {
    const threats = await this.threatDetectionRepository.findAll();
    const results: SearchResult[] = [];

    threats.forEach((threat) => {
      if (
        this.matchesQuery(threat.threatType, query) ||
        this.matchesQuery(threat.description || "", query) ||
        this.matchesQuery(threat.sourceUrl || "", query)
      ) {
        results.push({
          type: "threat",
          id: threat.id,
          title: `${threat.threatType} Threat`,
          description:
            threat.description || `${threat.threatType} threat detected`,
          metadata: {
            type: threat.threatType,
            severity: threat.severity,
            status: threat.status,
            source: threat.sourceUrl,
            detectedAt: threat.firstDetected,
          },
        });
      }
    });

    return results.slice(0, limit);
  }

  private async searchAlerts(
    query: string,
    limit: number
  ): Promise<SearchResult[]> {
    const alerts = await this.alertRepository.findAll();
    const results: SearchResult[] = [];

    alerts.forEach((alert) => {
      if (
        this.matchesQuery(alert.type, query) ||
        this.matchesQuery(alert.message, query)
      ) {
        results.push({
          type: "alert",
          id: alert.id,
          title: `${alert.type} Alert`,
          description: alert.message,
          metadata: {
            type: alert.type,
            priority: alert.priority,
            status: alert.status,
            createdAt: alert.createdAt,
          },
        });
      }
    });

    return results.slice(0, limit);
  }

  private matchesQuery(text: string, query: string): boolean {
    return text.toLowerCase().includes(query.toLowerCase());
  }

  private calculateRelevanceScore(result: SearchResult, query: string): number {
    let score = 0;

    // Exact match in title gets highest score
    if (result.title.toLowerCase() === query.toLowerCase()) {
      score += 100;
    } else if (result.title.toLowerCase().includes(query.toLowerCase())) {
      score += 50;
    }

    // Match in description gets medium score
    if (result.description.toLowerCase().includes(query.toLowerCase())) {
      score += 25;
    }

    // Boost score based on result type priority
    switch (result.type) {
      case "brand":
        score += 10;
        break;
      case "threat":
        score += 8;
        break;
      case "domain":
        score += 6;
        break;
      case "alert":
        score += 4;
        break;
    }

    return score;
  }
}
