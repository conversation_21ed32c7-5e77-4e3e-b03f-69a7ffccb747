import { Provider } from "@core/decorators/decorators";
import { Mo<PERSON> } from "@core/decorators/mock.decorator";

interface SearchOptions {
  query: string;
  types: string[];
  page: number;
  limit: number;
}

interface SearchResult {
  type: string;
  id: string;
  title: string;
  description: string;
  url?: string;
  metadata?: any;
}

interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

@Provider()
export class SearchService {
  constructor() {}

  @Mock("SearchService-search")
  async search(options: SearchOptions): Promise<SearchResponse> {
    const { query, types, page, limit } = options;

    // Simple mock implementation for now
    return {
      results: [],
      total: 0,
      page,
      limit,
      hasMore: false,
    };
  }

  @Mock("SearchService-suggest")
  async suggest(query: string): Promise<string[]> {
    if (!query || query.length < 2) {
      return [];
    }

    // Simple mock implementation for now
    return [`${query}-suggestion1`, `${query}-suggestion2`];
  }
}
