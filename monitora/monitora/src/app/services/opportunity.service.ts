import { Provider } from '@core/decorators/decorators';
import { OpportunityRepository } from '../repositories/opportunity.repository';
import { Opportunity } from '../../entities/opportunity.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class OpportunityService {
  constructor(private opportunityRepository: OpportunityRepository) {}

  @Mock('OpportunityService-findAll')
  async findAll(): Promise<Opportunity[]> {
    return this.opportunityRepository.findAll();
  }

  @Mock('OpportunityService-findById')
  async findById(id: string): Promise<Opportunity | null> {
    return this.opportunityRepository.findById(id);
  }

  @Mock('OpportunityService-create')
  async create(data: Partial<Opportunity>): Promise<Opportunity> {
    return this.opportunityRepository.create(data);
  }

  @Mock('OpportunityService-update')
  async update(id: string, data: Partial<Opportunity>): Promise<Opportunity | null> {
    return this.opportunityRepository.update(id, data);
  }

  @Mock('OpportunityService-delete')
  async delete(id: string): Promise<void> {
    return this.opportunityRepository.delete(id);
  }

  @Mock('OpportunityService-getPipelineView')
  async getPipelineView(): Promise<Record<string, Opportunity[]>> {
    const opportunities = await this.opportunityRepository.findAll();
    return opportunities.reduce((acc, opp) => {
      if (!acc[opp.stage]) {
        acc[opp.stage] = [];
      }
      acc[opp.stage].push(opp);
      return acc;
    }, {} as Record<string, Opportunity[]>);
  }
}