import { Provider } from '@core/decorators/decorators';
import { ContactRepository } from '../repositories/contact.repository';
import { Contact } from '../../entities/contact.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class ContactService {
  constructor(private contactRepository: ContactRepository) {}

  @Mock('ContactService-findAll')
  async findAll(): Promise<Contact[]> {
    return this.contactRepository.findAll();
  }

  @Mock('ContactService-findById')
  async findById(id: string): Promise<Contact | null> {
    return this.contactRepository.findById(id);
  }

  @Mock('ContactService-create')
  async create(data: Partial<Contact>): Promise<Contact> {
    const existingContact = await this.contactRepository.findByEmail(data.email);
    if (existingContact) {
      throw new Error('Contact with this email already exists');
    }
    return this.contactRepository.create(data);
  }

  @<PERSON><PERSON>('ContactService-update')
  async update(id: string, data: Partial<Contact>): Promise<Contact | null> {
    if (data.email) {
      const existingContact = await this.contactRepository.findByEmail(data.email);
      if (existingContact && existingContact.id !== id) {
        throw new Error('Contact with this email already exists');
      }
    }
    return this.contactRepository.update(id, data);
  }

  @Mock('ContactService-delete')
  async delete(id: string): Promise<void> {
    return this.contactRepository.delete(id);
  }

  @Mock('ContactService-getWithAddresses')
  async getWithAddresses(id: string): Promise<Contact | null> {
    return this.contactRepository.findWithAddresses(id);
  }
}