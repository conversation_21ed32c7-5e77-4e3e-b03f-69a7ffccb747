import { Provider } from "@core/decorators/decorators";
import { BrandRepository } from "../repositories/brand.repository";
import { Mock } from "@core/decorators/mock.decorator";
import { Brand } from "../../entities/brand.entity";

@Provider()
export class BrandService {
  constructor(private brandRepository: BrandRepository) {}

  @Mock("BrandService-create")
  async create(data: Partial<Brand>): Promise<Brand> {
    // Set default monitoring settings if not provided
    if (!data.monitoringSettings) {
      data.monitoringSettings = {
        domainMonitoring: true,
        socialMediaMonitoring: true,
        threatDetection: true,
        alertFrequency: "realtime",
        sensitivityLevel: "medium",
      };
    }

    // Initialize keywords from brand name if not provided
    if (!data.keywords && data.name) {
      data.keywords = [data.name.toLowerCase()];
    }

    // Initialize protected terms from brand name if not provided
    if (!data.protectedTerms && data.name) {
      data.protectedTerms = [data.name.toLowerCase()];
    }

    return this.brandRepository.create(data);
  }

  @Mock("BrandService-update")
  async update(id: string, data: Partial<Brand>): Promise<Brand | null> {
    return this.brandRepository.update(id, data);
  }

  @Mock("BrandService-findAll")
  async findAll(): Promise<Brand[]> {
    return this.brandRepository.findAll();
  }

  @Mock("BrandService-findById")
  async findById(id: string): Promise<Brand | null> {
    return this.brandRepository.findById(id);
  }

  @Mock("BrandService-findByOwner")
  async findByOwner(ownerId: string): Promise<Brand[]> {
    return this.brandRepository.findByOwner(ownerId);
  }

  @Mock("BrandService-findByDomain")
  async findByDomain(domain: string): Promise<Brand | null> {
    return this.brandRepository.findByDomain(domain);
  }

  @Mock("BrandService-delete")
  async delete(id: string): Promise<void> {
    return this.brandRepository.delete(id);
  }

  @Mock("BrandService-addKeyword")
  async addKeyword(id: string, keyword: string): Promise<Brand | null> {
    const brand = await this.findById(id);
    if (!brand) return null;

    if (!brand.keywords) {
      brand.keywords = [];
    }

    if (!brand.keywords.includes(keyword.toLowerCase())) {
      brand.keywords.push(keyword.toLowerCase());
      return this.update(id, { keywords: brand.keywords });
    }

    return brand;
  }

  @Mock("BrandService-removeKeyword")
  async removeKeyword(id: string, keyword: string): Promise<Brand | null> {
    const brand = await this.findById(id);
    if (!brand) return null;

    if (brand.keywords) {
      brand.keywords = brand.keywords.filter(
        (k) => k !== keyword.toLowerCase()
      );
      return this.update(id, { keywords: brand.keywords });
    }

    return brand;
  }

  @Mock("BrandService-updateMonitoringSettings")
  async updateMonitoringSettings(
    id: string,
    settings: Partial<Brand["monitoringSettings"]>
  ): Promise<Brand | null> {
    const brand = await this.findById(id);
    if (!brand) return null;

    const updatedSettings = {
      ...brand.monitoringSettings,
      ...settings,
    };

    return this.update(id, { monitoringSettings: updatedSettings });
  }

  @Mock("BrandService-getActiveBrands")
  async getActiveBrands(): Promise<Brand[]> {
    return this.brandRepository.findByStatus("active");
  }

  @Mock("BrandService-getBrandStats")
  async getBrandStats(id: string): Promise<{
    totalThreats: number;
    activeThreats: number;
    resolvedThreats: number;
    domainMonitors: number;
    socialMediaMonitors: number;
    alerts: number;
    takedownRequests: number;
  } | null> {
    return this.brandRepository.getBrandStats(id);
  }

  @Mock("BrandService-addDomain")
  async addDomain(id: string, domain: string): Promise<Brand | null> {
    const brand = await this.findById(id);
    if (!brand) return null;

    if (!brand.additionalDomains) {
      brand.additionalDomains = [];
    }

    if (!brand.additionalDomains.includes(domain.toLowerCase())) {
      brand.additionalDomains.push(domain.toLowerCase());
      return this.update(id, { additionalDomains: brand.additionalDomains });
    }

    return brand;
  }

  @Mock("BrandService-removeDomain")
  async removeDomain(id: string, domain: string): Promise<Brand | null> {
    const brand = await this.findById(id);
    if (!brand) return null;

    if (brand.additionalDomains) {
      brand.additionalDomains = brand.additionalDomains.filter(
        (d) => d !== domain.toLowerCase()
      );
      return this.update(id, { additionalDomains: brand.additionalDomains });
    }

    return brand;
  }

  @Mock("BrandService-getAllDomains")
  async getAllDomains(id: string): Promise<string[]> {
    const brand = await this.findById(id);
    if (!brand) return [];

    const domains = [brand.primaryDomain];
    if (brand.additionalDomains) {
      domains.push(...brand.additionalDomains);
    }

    return domains;
  }
}
