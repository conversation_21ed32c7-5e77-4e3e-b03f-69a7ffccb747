import { Provider } from '@core/decorators/decorators';
import { IntegrationType } from '../types/integration.types';

@Provider()
export class IntegrationService {
  async sync(integrationType: IntegrationType, data: any) {
    const integration = await this.getIntegrationConfig(integrationType);
    return this.executeSync(integration, data);
  }

  async webhook(integrationType: IntegrationType, payload: any) {
    const integration = await this.getIntegrationConfig(integrationType);
    return this.processWebhook(integration, payload);
  }
}