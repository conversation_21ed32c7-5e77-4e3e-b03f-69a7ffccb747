import { Provider } from '@core/decorators/decorators';
import { UserService } from './user.service';
import { TokenProvider } from '../providers/token.provider';
import { Mock } from '@core/decorators/mock.decorator';
import * as bcrypt from 'bcrypt';

@Provider()
export class AuthService {
  constructor(
    private userService: UserService,
    private tokenProvider: TokenProvider
  ) {}

  @Mock('AuthService-login')
  async login(username: string, password: string) {
    const user = await this.userService.getUser(username);
    if (!user) {
      throw new Error('User not found');
    }

    const isValid = await bcrypt.compare(password, user.password);
    if (!isValid) {
      throw new Error('Invalid password');
    }

    const token = this.tokenProvider.getToken(user);
    return { user, token };
  }

  @Mock('AuthService-verifyToken')
  verifyToken(token: string) {
    return this.tokenProvider.verifyToken(token);
  }
}
