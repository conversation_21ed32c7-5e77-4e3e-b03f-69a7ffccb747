import { Provider } from '@core/decorators/decorators';
import { TaskRepository } from '../repositories/task.repository';
import { Task } from '../../entities/task.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class TaskService {
  constructor(private taskRepository: TaskRepository) {}

  @Mock('TaskService-findAll')
  async findAll(): Promise<Task[]> {
    return this.taskRepository.findAll();
  }

  @Mock('TaskService-findById')
  async findById(id: string): Promise<Task | null> {
    return this.taskRepository.findWithRelations(id);
  }

  @Mock('TaskService-findByAssignee')
  async findByAssignee(userId: number): Promise<Task[]> {
    return this.taskRepository.findByAssignee(userId);
  }

  @Mock('TaskService-create')
  async create(data: Partial<Task>): Promise<Task> {
    return this.taskRepository.create(data);
  }

  @Mock('TaskService-update')
  async update(id: string, data: Partial<Task>): Promise<Task | null> {
    return this.taskRepository.update(id, data);
  }

  @Mock('TaskService-delete')
  async delete(id: string): Promise<void> {
    return this.taskRepository.delete(id);
  }
}