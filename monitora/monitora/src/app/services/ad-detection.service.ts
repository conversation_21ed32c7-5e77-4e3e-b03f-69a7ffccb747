import { Provider } from '@core/decorators/decorators';
import { MetaGateway } from '../gateways/meta.gateway';
import { GoogleGateway } from '../gateways/google.gateway';
import { Mock } from '@core/decorators/mock.decorator';
import { Brand } from '../../entities/brand.entity';

@Provider()
export class AdDetectionService {
  constructor(
    private metaGateway: MetaGateway,
    private googleGateway: GoogleGateway
  ) {}

  @Mock('AdDetectionService-scanForScamAds')
  async scanForScamAds(brand: Brand): Promise<{
    platform: string;
    adId: string;
    content: string;
    targetUrl: string;
    isScam: boolean;
    scamIndicators: string[];
    riskScore: number;
    evidence: any;
  }[]> {
    const scamAds = [];

    // Scan Facebook/Instagram ads
    const metaAds = await this.scanMetaAds(brand);
    scamAds.push(...metaAds);

    // Scan Google ads (through web search)
    const googleAds = await this.scanGoogleAds(brand);
    scamAds.push(...googleAds);

    return scamAds;
  }

  private async scanMetaAds(brand: Brand): Promise<any[]> {
    const scamAds = [];

    for (const keyword of brand.keywords || []) {
      try {
        // Search Meta Ad Library
        const ads = await this.metaGateway.searchAds(keyword);
        
        for (const ad of ads) {
          const analysis = this.analyzeAdForScam(ad, brand, 'meta');
          if (analysis.isScam) {
            scamAds.push({
              platform: 'meta',
              adId: ad.id,
              content: this.extractAdContent(ad),
              targetUrl: this.extractAdUrl(ad),
              ...analysis
            });
          }
        }
      } catch (error) {
        console.error(`Error scanning Meta ads for ${keyword}:`, error);
      }
    }

    return scamAds;
  }

  private async scanGoogleAds(brand: Brand): Promise<any[]> {
    const scamAds = [];

    for (const keyword of brand.keywords || []) {
      try {
        // Search for potential Google ads
        const results = await this.googleGateway.searchWeb(
          `"${keyword}" site:googleadservices.com OR site:googlesyndication.com OR "sponsored" OR "ad"`
        );
        
        for (const result of results) {
          const analysis = this.analyzeAdForScam(result, brand, 'google');
          if (analysis.isScam) {
            scamAds.push({
              platform: 'google',
              adId: result.link || result.url,
              content: result.snippet || result.title,
              targetUrl: result.link,
              ...analysis
            });
          }
        }
      } catch (error) {
        console.error(`Error scanning Google ads for ${keyword}:`, error);
      }
    }

    return scamAds;
  }

  private analyzeAdForScam(ad: any, brand: Brand, platform: string): {
    isScam: boolean;
    scamIndicators: string[];
    riskScore: number;
    evidence: any;
  } {
    const scamIndicators: string[] = [];
    let riskScore = 0;

    const content = this.extractAdContent(ad);
    const targetUrl = this.extractAdUrl(ad);

    // 1. Check for brand impersonation
    if (this.containsBrandTerms(content, brand)) {
      scamIndicators.push('Contains brand terms without authorization');
      riskScore += 30;
    }

    // 2. Check for suspicious URLs
    if (this.isSuspiciousUrl(targetUrl, brand)) {
      scamIndicators.push('Suspicious target URL');
      riskScore += 40;
    }

    // 3. Check for scam keywords
    const scamKeywords = this.detectScamKeywords(content);
    if (scamKeywords.length > 0) {
      scamIndicators.push(`Scam keywords detected: ${scamKeywords.join(', ')}`);
      riskScore += scamKeywords.length * 15;
    }

    // 4. Check for fake promotions
    if (this.isFakePromotion(content, brand)) {
      scamIndicators.push('Potential fake promotion or giveaway');
      riskScore += 35;
    }

    // 5. Check for urgency tactics
    if (this.hasUrgencyTactics(content)) {
      scamIndicators.push('Uses urgency/pressure tactics');
      riskScore += 20;
    }

    // 6. Check for too-good-to-be-true offers
    if (this.isTooGoodToBeTrueOffer(content)) {
      scamIndicators.push('Unrealistic discount or offer');
      riskScore += 25;
    }

    // 7. Platform-specific checks
    if (platform === 'meta') {
      const metaSpecificRisk = this.analyzeMetaAdSpecific(ad, brand);
      scamIndicators.push(...metaSpecificRisk.indicators);
      riskScore += metaSpecificRisk.score;
    }

    return {
      isScam: riskScore >= 50, // Threshold for scam classification
      scamIndicators,
      riskScore: Math.min(riskScore, 100),
      evidence: {
        content,
        targetUrl,
        platform,
        analysisDate: new Date(),
        rawData: ad
      }
    };
  }

  private containsBrandTerms(content: string, brand: Brand): boolean {
    const contentLower = content.toLowerCase();
    
    // Check brand name
    if (contentLower.includes(brand.name.toLowerCase())) {
      return true;
    }

    // Check keywords
    return brand.keywords?.some(keyword => 
      contentLower.includes(keyword.toLowerCase())
    ) || false;
  }

  private isSuspiciousUrl(url: string, brand: Brand): boolean {
    if (!url) return false;

    const urlLower = url.toLowerCase();
    
    // Check if URL contains brand terms but isn't official domain
    const allDomains = [brand.primaryDomain];
    if (brand.additionalDomains) {
      allDomains.push(...brand.additionalDomains);
    }

    const containsBrandTerms = brand.keywords?.some(keyword => 
      urlLower.includes(keyword.toLowerCase())
    ) || urlLower.includes(brand.name.toLowerCase());

    const isOfficialDomain = allDomains.some(domain => 
      urlLower.includes(domain.toLowerCase())
    );

    return containsBrandTerms && !isOfficialDomain;
  }

  private detectScamKeywords(content: string): string[] {
    const scamKeywords = [
      'free', 'win', 'winner', 'congratulations', 'limited time',
      'act now', 'urgent', 'expires today', 'click here now',
      'guaranteed', '100% off', 'risk free', 'no questions asked',
      'secret', 'exclusive', 'selected', 'chosen', 'lucky',
      'cash prize', 'gift card', 'bitcoin', 'cryptocurrency',
      'investment opportunity', 'make money fast', 'work from home',
      'lose weight fast', 'miracle cure', 'doctor recommended'
    ];

    const contentLower = content.toLowerCase();
    return scamKeywords.filter(keyword => contentLower.includes(keyword));
  }

  private isFakePromotion(content: string, brand: Brand): boolean {
    const contentLower = content.toLowerCase();
    
    const promotionKeywords = ['giveaway', 'contest', 'free', 'win', 'prize'];
    const hasPromotionKeywords = promotionKeywords.some(keyword => 
      contentLower.includes(keyword)
    );

    const hasBrandTerms = this.containsBrandTerms(content, brand);

    return hasPromotionKeywords && hasBrandTerms;
  }

  private hasUrgencyTactics(content: string): boolean {
    const urgencyKeywords = [
      'limited time', 'expires', 'hurry', 'act now', 'today only',
      'last chance', 'don\'t miss', 'urgent', 'immediate'
    ];

    const contentLower = content.toLowerCase();
    return urgencyKeywords.some(keyword => contentLower.includes(keyword));
  }

  private isTooGoodToBeTrueOffer(content: string): boolean {
    const contentLower = content.toLowerCase();
    
    // Check for extreme discounts
    const discountRegex = /(\d+)%\s*off|(\d+)%\s*discount/gi;
    const matches = contentLower.match(discountRegex);
    
    if (matches) {
      const percentages = matches.map(match => {
        const num = match.match(/\d+/);
        return num ? parseInt(num[0]) : 0;
      });
      
      if (percentages.some(p => p >= 90)) {
        return true;
      }
    }

    // Check for "free" high-value items
    const freeValueRegex = /free.*(iphone|ipad|macbook|tesla|car|house|money)/gi;
    return freeValueRegex.test(contentLower);
  }

  private analyzeMetaAdSpecific(ad: any, brand: Brand): {
    indicators: string[];
    score: number;
  } {
    const indicators: string[] = [];
    let score = 0;

    // Check ad spend (low spend might indicate test/scam ad)
    if (ad.spend && ad.spend.lower_bound < 100) {
      indicators.push('Low ad spend (potential test ad)');
      score += 15;
    }

    // Check page verification
    if (ad.page_name && !ad.page_name.includes('verified')) {
      indicators.push('Unverified page running ads');
      score += 10;
    }

    // Check ad delivery period (very short might be scam)
    if (ad.ad_delivery_start_time && ad.ad_delivery_stop_time) {
      const start = new Date(ad.ad_delivery_start_time);
      const stop = new Date(ad.ad_delivery_stop_time);
      const durationDays = (stop.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
      
      if (durationDays < 3) {
        indicators.push('Very short ad campaign duration');
        score += 20;
      }
    }

    return { indicators, score };
  }

  private extractAdContent(ad: any): string {
    // Extract content based on platform
    if (ad.ad_creative_bodies) {
      return ad.ad_creative_bodies.join(' ');
    }
    
    if (ad.snippet) {
      return ad.snippet;
    }
    
    if (ad.title) {
      return ad.title;
    }
    
    if (ad.message) {
      return ad.message;
    }

    return '';
  }

  private extractAdUrl(ad: any): string {
    if (ad.ad_creative_link_captions) {
      return ad.ad_creative_link_captions[0] || '';
    }
    
    if (ad.link) {
      return ad.link;
    }
    
    if (ad.url) {
      return ad.url;
    }

    return '';
  }

  @Mock('AdDetectionService-reportScamAd')
  async reportScamAd(platform: string, adId: string, reason: string): Promise<boolean> {
    try {
      switch (platform) {
        case 'meta':
          return await this.metaGateway.reportContent(adId, reason);
        case 'google':
          // Google ads reporting would need specific implementation
          console.log(`Would report Google ad ${adId} for: ${reason}`);
          return true;
        default:
          return false;
      }
    } catch (error) {
      console.error(`Error reporting ${platform} ad ${adId}:`, error);
      return false;
    }
  }
}
