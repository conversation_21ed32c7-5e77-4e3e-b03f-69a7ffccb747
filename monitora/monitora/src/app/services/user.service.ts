import { Provider } from '@core/decorators/decorators';
import { UserRepository } from '../repositories/user.repository';
import { Mock } from '@core/decorators/mock.decorator';
import { User } from '../../entities/user.entity';

@Provider()
export class UserService {
  constructor(private userRepository: UserRepository) {}

  @Mock('UserService-create')
  async create(data: Partial<User>) {
    return this.userRepository.create(data);
  }

  @Mock('UserService-update')
  async update(id: string, data: Partial<User>) {
    return this.userRepository.update(id, data);
  }

  async getUser(username: string) {
    const user = await this.userRepository.findUser(username);

    if (!user) {
      return { success: false, message: 'User not found' };
    }

    return {
      success: true,
      user: { id: user.id, name: user.name, username: user.username },
    };
  }
}
