import { Provider } from '@core/decorators/decorators';
import { ProductRepository } from '../repositories/product.repository';
import { Product } from '../../entities/product.entity';
import { Mock } from '@core/decorators/mock.decorator';

@Provider()
export class ProductService {
  constructor(private productRepository: ProductRepository) {}

  @Mock('ProductService-findAll')
  async findAll(): Promise<Product[]> {
    return this.productRepository.findAll();
  }

  @Mock('ProductService-findById')
  async findById(id: string): Promise<Product | null> {
    return this.productRepository.findById(id);
  }

  @Mock('ProductService-create')
  async create(data: Partial<Product>): Promise<Product> {
    if (data.sku) {
      const existing = await this.productRepository.findBySku(data.sku);
      if (existing) {
        throw new Error('Product with this SKU already exists');
      }
    }
    return this.productRepository.create(data);
  }

  @Mock('ProductService-update')
  async update(id: string, data: Partial<Product>): Promise<Product | null> {
    if (data.sku) {
      const existing = await this.productRepository.findBySku(data.sku);
      if (existing && existing.id !== id) {
        throw new Error('Product with this SKU already exists');
      }
    }
    return this.productRepository.update(id, data);
  }

  @Mock('ProductService-delete')
  async delete(id: string): Promise<void> {
    return this.productRepository.delete(id);
  }

  @Mock('ProductService-getWithFeatures')
  async getWithFeatures(id: string): Promise<Product | null> {
    return this.productRepository.findWithFeatures(id);
  }

  @Mock('ProductService-findByCategory')
  async findByCategory(category: string): Promise<Product[]> {
    return this.productRepository.findByCategory(category);
  }
}