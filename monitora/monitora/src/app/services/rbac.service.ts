import { Provider } from "@core/decorators/decorators";
import { <PERSON><PERSON> } from "@core/decorators/mock.decorator";
import { UserRepository } from "../repositories/user.repository";
import { RoleRepository } from "../repositories/role.repository";
import { PermissionRepository } from "../repositories/permission.repository";

@Provider()
export class RBACService {
  constructor(
    private userRepository: UserRepository,
    private roleRepository: RoleRepository,
    private permissionRepository: PermissionRepository
  ) {}

  @Mock("RBACService-checkPermission")
  async checkPermission(
    userId: string,
    resource: string,
    action: string
  ): Promise<boolean> {
    try {
      const user = await this.userRepository.findWithRoles(userId);
      if (!user) return false;

      // System admin has all permissions
      if (user.defaultRole === "system_admin") return true;

      // Check user's roles and their permissions
      for (const role of user.roles || []) {
        const hasPermission = await this.roleHasPermission(role.id, resource, action);
        if (hasPermission) return true;
      }

      // Check default role permissions
      const defaultRolePermissions = this.getDefaultRolePermissions(user.defaultRole);
      return this.checkDefaultPermission(defaultRolePermissions, resource, action);
    } catch (error) {
      console.error("Error checking permission:", error);
      return false;
    }
  }

  @Mock("RBACService-roleHasPermission")
  async roleHasPermission(
    roleId: string,
    resource: string,
    action: string
  ): Promise<boolean> {
    const role = await this.roleRepository.findWithPermissions(roleId);
    if (!role) return false;

    return role.permissions.some(
      (permission) =>
        permission.resource === resource && permission.action === action
    );
  }

  @Mock("RBACService-getUserPermissions")
  async getUserPermissions(userId: string): Promise<string[]> {
    const user = await this.userRepository.findWithRoles(userId);
    if (!user) return [];

    const permissions = new Set<string>();

    // Add default role permissions
    const defaultPermissions = this.getDefaultRolePermissions(user.defaultRole);
    defaultPermissions.forEach(perm => permissions.add(perm));

    // Add role-based permissions
    for (const role of user.roles || []) {
      const roleWithPermissions = await this.roleRepository.findWithPermissions(role.id);
      if (roleWithPermissions) {
        roleWithPermissions.permissions.forEach(permission => {
          permissions.add(`${permission.resource}:${permission.action}`);
        });
      }
    }

    return Array.from(permissions);
  }

  @Mock("RBACService-assignRoleToUser")
  async assignRoleToUser(userId: string, roleId: string): Promise<boolean> {
    try {
      const user = await this.userRepository.findWithRoles(userId);
      const role = await this.roleRepository.findById(roleId);

      if (!user || !role) return false;

      if (!user.roles) user.roles = [];
      
      // Check if role is already assigned
      if (user.roles.some(r => r.id === roleId)) return true;

      user.roles.push(role);
      await this.userRepository.update(userId, { roles: user.roles });
      return true;
    } catch (error) {
      console.error("Error assigning role to user:", error);
      return false;
    }
  }

  @Mock("RBACService-removeRoleFromUser")
  async removeRoleFromUser(userId: string, roleId: string): Promise<boolean> {
    try {
      const user = await this.userRepository.findWithRoles(userId);
      if (!user || !user.roles) return false;

      user.roles = user.roles.filter(role => role.id !== roleId);
      await this.userRepository.update(userId, { roles: user.roles });
      return true;
    } catch (error) {
      console.error("Error removing role from user:", error);
      return false;
    }
  }

  private getDefaultRolePermissions(role: string): string[] {
    const permissions: { [key: string]: string[] } = {
      system_admin: [
        "users:create", "users:read", "users:update", "users:delete", "users:manage",
        "brands:create", "brands:read", "brands:update", "brands:delete", "brands:manage",
        "threats:create", "threats:read", "threats:update", "threats:delete", "threats:manage",
        "alerts:create", "alerts:read", "alerts:update", "alerts:delete", "alerts:manage",
        "reports:create", "reports:read", "reports:update", "reports:delete", "reports:manage",
        "settings:read", "settings:update", "settings:manage",
        "revenue:read", "revenue:manage",
        "system:manage"
      ],
      admin: [
        "users:read", "users:update",
        "brands:create", "brands:read", "brands:update", "brands:delete",
        "threats:read", "threats:update", "threats:manage",
        "alerts:read", "alerts:update", "alerts:manage",
        "reports:create", "reports:read", "reports:update",
        "settings:read", "settings:update"
      ],
      editor: [
        "brands:read", "brands:update",
        "threats:read", "threats:update",
        "alerts:read", "alerts:update",
        "reports:create", "reports:read"
      ],
      viewer: [
        "brands:read",
        "threats:read",
        "alerts:read",
        "reports:read"
      ]
    };

    return permissions[role] || [];
  }

  private checkDefaultPermission(
    permissions: string[],
    resource: string,
    action: string
  ): boolean {
    const permissionString = `${resource}:${action}`;
    const managePermission = `${resource}:manage`;
    
    return permissions.includes(permissionString) || permissions.includes(managePermission);
  }

  @Mock("RBACService-initializeDefaultRoles")
  async initializeDefaultRoles(): Promise<void> {
    const defaultRoles = [
      {
        name: "System Administrator",
        description: "Full system access with all permissions",
        isSystemRole: true
      },
      {
        name: "Brand Administrator", 
        description: "Manage brands and monitor threats",
        isSystemRole: true
      },
      {
        name: "Brand Editor",
        description: "Edit brand information and respond to threats",
        isSystemRole: true
      },
      {
        name: "Brand Viewer",
        description: "View brand information and threat reports",
        isSystemRole: true
      }
    ];

    for (const roleData of defaultRoles) {
      const existingRole = await this.roleRepository.findByName(roleData.name);
      if (!existingRole) {
        await this.roleRepository.create(roleData);
      }
    }
  }
}
