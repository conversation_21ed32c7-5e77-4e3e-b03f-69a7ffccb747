import { Provider } from '@core/decorators/decorators';
import { <PERSON><PERSON> } from '@core/decorators/mock.decorator';
import { EmailProvider } from '../providers/email.provider';

@Provider()
export class EmailService {
  constructor(private emailProvider: EmailProvider) {}

  @Mock('EmailService-syncEmails')
  async syncEmails(userId: string): Promise<void> {
    await this.emailProvider.syncUserEmails(userId);
  }

  @Mock('EmailService-sendTemplatedEmail')
  async sendTemplatedEmail(templateId: string, data: any): Promise<void> {
    await this.emailProvider.sendTemplate(templateId, data);
  }

  @Mock('EmailService-trackEmailActivity')
  async trackEmailActivity(emailId: string): Promise<void> {
    await this.emailProvider.trackActivity(emailId);
  }
}