import { Provider } from '@core/decorators/decorators';
import { WorkflowEngine } from '../engines/workflow.engine';

@Provider()
export class WorkflowService {
  constructor(private workflowEngine: WorkflowEngine) {}

  async executeWorkflow(workflowId: string, context: any) {
    const workflow = await this.workflowEngine.load(workflowId);
    return this.workflowEngine.execute(workflow, context);
  }

  async createTrigger(entityType: string, event: string, workflowId: string) {
    return this.workflowEngine.createTrigger({
      entityType,
      event,
      workflowId
    });
  }
}