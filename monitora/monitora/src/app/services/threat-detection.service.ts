import { Provider } from "@core/decorators/decorators";
import { ThreatDetectionRepository } from "../repositories/threat-detection.repository";
import { AlertService } from "./alert.service";
import { <PERSON><PERSON> } from "@core/decorators/mock.decorator";
import { ThreatDetection } from "../../entities/threat-detection.entity";
import { DomainMonitor } from "../../entities/domain-monitor.entity";
import { SocialMediaMonitor } from "../../entities/social-media-monitor.entity";

@Provider()
export class ThreatDetectionService {
  constructor(
    private threatDetectionRepository: ThreatDetectionRepository,
    private alertService: AlertService
  ) {}

  @Mock("ThreatDetectionService-createFromDomainMonitor")
  async createFromDomainMonitor(
    domainMonitor: DomainMonitor
  ): Promise<ThreatDetection> {
    const severity = this.calculateSeverityFromRiskFactors(
      domainMonitor.riskFactors
    );
    const confidenceScore = this.calculateConfidenceScore(domainMonitor);

    const threatDetection = await this.threatDetectionRepository.create({
      sourceType: "domain",
      sourceUrl: `https://${domainMonitor.domain}`,
      severity,
      status: "pending",
      confidenceScore,
      threatType: this.mapDetectionTypeToThreatType(
        domainMonitor.detectionType
      ),
      description: `Suspicious domain detected: ${domainMonitor.domain}`,
      evidence: {
        screenshots: domainMonitor.websiteData?.screenshots || [],
        textContent: domainMonitor.websiteData?.content || "",
        metadata: {
          whoisData: domainMonitor.whoisData,
          similarityScore: domainMonitor.similarityScore,
          detectionType: domainMonitor.detectionType,
        },
        technicalDetails: {
          dnsRecords: domainMonitor.dnsRecords,
          ssl: domainMonitor.websiteData?.ssl,
        },
      },
      riskAssessment: this.generateRiskAssessment(domainMonitor),
      detectionMethods: [
        {
          method: "Domain similarity analysis",
          confidence: domainMonitor.similarityScore,
          details: `Similarity score: ${domainMonitor.similarityScore}`,
        },
      ],
      firstDetected: new Date(),
      lastUpdated: new Date(),
      brandId: domainMonitor.brandId,
      domainMonitorId: domainMonitor.id,
    });

    // Create alert if high severity
    if (severity === "high" || severity === "critical") {
      await this.alertService.createThreatAlert(threatDetection);
    }

    return threatDetection;
  }

  @Mock("ThreatDetectionService-createFromSocialMediaMonitor")
  async createFromSocialMediaMonitor(
    socialMediaMonitor: SocialMediaMonitor
  ): Promise<ThreatDetection> {
    const severity = this.calculateSeverityFromRiskFactors(
      socialMediaMonitor.riskFactors
    );
    const confidenceScore = socialMediaMonitor.similarityScore;

    const threatDetection = await this.threatDetectionRepository.create({
      sourceType: "social_media",
      sourceUrl: socialMediaMonitor.contentUrl,
      severity,
      status: "pending",
      confidenceScore,
      threatType: this.mapSocialMediaDetectionTypeToThreatType(
        socialMediaMonitor.detectionType
      ),
      description: `Suspicious ${socialMediaMonitor.platform} ${socialMediaMonitor.contentType} detected`,
      evidence: {
        screenshots: socialMediaMonitor.mediaUrls || [],
        textContent: socialMediaMonitor.content,
        metadata: {
          platform: socialMediaMonitor.platform,
          contentType: socialMediaMonitor.contentType,
          authorUsername: socialMediaMonitor.authorUsername,
          authorProfile: socialMediaMonitor.authorProfile,
          engagement: socialMediaMonitor.engagement,
          hashtags: socialMediaMonitor.hashtags,
          mentions: socialMediaMonitor.mentions,
        },
        technicalDetails: {},
      },
      riskAssessment:
        this.generateSocialMediaRiskAssessment(socialMediaMonitor),
      detectionMethods: [
        {
          method: "Social media content analysis",
          confidence: confidenceScore,
          details: `Content similarity and pattern matching`,
        },
      ],
      firstDetected: new Date(),
      lastUpdated: new Date(),
      brandId: socialMediaMonitor.brandId,
      socialMediaMonitorId: socialMediaMonitor.id,
    });

    // Create alert if high severity
    if (severity === "high" || severity === "critical") {
      await this.alertService.createThreatAlert(threatDetection);
    }

    return threatDetection;
  }

  @Mock("ThreatDetectionService-updateStatus")
  async updateStatus(
    id: string,
    status: ThreatDetection["status"],
    notes?: string
  ): Promise<ThreatDetection | null> {
    const updateData: Partial<ThreatDetection> = {
      status,
      lastUpdated: new Date(),
    };

    if (notes) {
      updateData.analystNotes = notes;
    }

    if (status === "resolved") {
      updateData.resolvedAt = new Date();
    }

    return this.threatDetectionRepository.update(id, updateData);
  }

  @Mock("ThreatDetectionService-addMitigationStep")
  async addMitigationStep(
    id: string,
    step: {
      step: string;
      assignedTo: string;
      dueDate: Date;
      notes?: string;
    }
  ): Promise<ThreatDetection | null> {
    const threat = await this.threatDetectionRepository.findById(id);
    if (!threat) return null;

    const mitigationSteps = threat.mitigationSteps || [];
    mitigationSteps.push({
      ...step,
      status: "pending",
      notes: step.notes || "",
    });

    return this.threatDetectionRepository.update(id, {
      mitigationSteps,
      lastUpdated: new Date(),
    });
  }

  @Mock("ThreatDetectionService-updateMitigationStep")
  async updateMitigationStep(
    id: string,
    stepIndex: number,
    updates: {
      status?: "pending" | "in_progress" | "completed" | "failed";
      notes?: string;
    }
  ): Promise<ThreatDetection | null> {
    const threat = await this.threatDetectionRepository.findById(id);
    if (
      !threat ||
      !threat.mitigationSteps ||
      !threat.mitigationSteps[stepIndex]
    ) {
      return null;
    }

    threat.mitigationSteps[stepIndex] = {
      ...threat.mitigationSteps[stepIndex],
      ...updates,
    };

    return this.threatDetectionRepository.update(id, {
      mitigationSteps: threat.mitigationSteps,
      lastUpdated: new Date(),
    });
  }

  @Mock("ThreatDetectionService-getThreatsForBrand")
  async getThreatsForBrand(
    brandId: string,
    filters?: {
      status?: ThreatDetection["status"];
      severity?: ThreatDetection["severity"];
      threatType?: ThreatDetection["threatType"];
      sourceType?: ThreatDetection["sourceType"];
    }
  ): Promise<ThreatDetection[]> {
    return this.threatDetectionRepository.findByBrand(brandId, filters);
  }

  @Mock("ThreatDetectionService-getThreatStats")
  async getThreatStats(brandId?: string): Promise<{
    total: number;
    byStatus: { [key: string]: number };
    bySeverity: { [key: string]: number };
    byThreatType: { [key: string]: number };
    bySourceType: { [key: string]: number };
  }> {
    return this.threatDetectionRepository.getStats(brandId);
  }

  private calculateSeverityFromRiskFactors(
    riskFactors: any[]
  ): ThreatDetection["severity"] {
    if (!riskFactors || riskFactors.length === 0) return "low";

    const criticalCount = riskFactors.filter(
      (f) => f.severity === "critical"
    ).length;
    const highCount = riskFactors.filter((f) => f.severity === "high").length;
    const mediumCount = riskFactors.filter(
      (f) => f.severity === "medium"
    ).length;

    if (criticalCount > 0) return "critical";
    if (highCount > 1) return "critical";
    if (highCount > 0) return "high";
    if (mediumCount > 2) return "high";
    if (mediumCount > 0) return "medium";

    return "low";
  }

  private calculateConfidenceScore(domainMonitor: DomainMonitor): number {
    let score = domainMonitor.similarityScore * 100;

    // Adjust based on risk factors
    if (domainMonitor.riskFactors) {
      const criticalFactors = domainMonitor.riskFactors.filter(
        (f) => f.severity === "critical"
      ).length;
      const highFactors = domainMonitor.riskFactors.filter(
        (f) => f.severity === "high"
      ).length;

      score += criticalFactors * 20;
      score += highFactors * 10;
    }

    return Math.min(score, 100);
  }

  private mapDetectionTypeToThreatType(
    detectionType: DomainMonitor["detectionType"]
  ): ThreatDetection["threatType"] {
    const mapping: {
      [key in DomainMonitor["detectionType"]]: ThreatDetection["threatType"];
    } = {
      typosquatting: "typosquatting",
      homograph: "typosquatting",
      subdomain: "typosquatting",
      tld_variation: "typosquatting",
      keyword_stuffing: "trademark_infringement",
      phishing: "phishing",
      malware: "malware",
    };

    return mapping[detectionType] || "trademark_infringement";
  }

  private mapSocialMediaDetectionTypeToThreatType(
    detectionType: SocialMediaMonitor["detectionType"]
  ): ThreatDetection["threatType"] {
    const mapping: {
      [key in SocialMediaMonitor["detectionType"]]: ThreatDetection["threatType"];
    } = {
      impersonation: "impersonation",
      trademark_infringement: "trademark_infringement",
      fake_promotion: "scam",
      phishing: "phishing",
      scam: "scam",
      counterfeit: "counterfeit",
    };

    return mapping[detectionType] || "impersonation";
  }

  private generateRiskAssessment(
    domainMonitor: DomainMonitor
  ): ThreatDetection["riskAssessment"] {
    const severity = this.calculateSeverityFromRiskFactors(
      domainMonitor.riskFactors
    );

    return {
      impactLevel: severity,
      affectedUsers: this.estimateAffectedUsers(domainMonitor),
      potentialDamage: this.assessPotentialDamage(domainMonitor),
      urgency: severity,
      businessImpact: this.assessBusinessImpact(domainMonitor),
    };
  }

  private generateSocialMediaRiskAssessment(
    socialMediaMonitor: SocialMediaMonitor
  ): ThreatDetection["riskAssessment"] {
    const severity = this.calculateSeverityFromRiskFactors(
      socialMediaMonitor.riskFactors
    );

    return {
      impactLevel: severity,
      affectedUsers: socialMediaMonitor.engagement?.views || 0,
      potentialDamage:
        this.assessSocialMediaPotentialDamage(socialMediaMonitor),
      urgency: severity,
      businessImpact: this.assessSocialMediaBusinessImpact(socialMediaMonitor),
    };
  }

  private estimateAffectedUsers(domainMonitor: DomainMonitor): number {
    // Simple estimation based on domain age and similarity
    const baseUsers = 100;
    const similarityMultiplier = domainMonitor.similarityScore * 10;
    return Math.floor(baseUsers * similarityMultiplier);
  }

  private assessPotentialDamage(domainMonitor: DomainMonitor): string {
    if (domainMonitor.detectionType === "phishing") {
      return "High risk of credential theft and financial fraud";
    }
    if (domainMonitor.detectionType === "malware") {
      return "Risk of malware distribution and system compromise";
    }
    return "Brand reputation damage and customer confusion";
  }

  private assessBusinessImpact(domainMonitor: DomainMonitor): string {
    return `Potential trademark infringement and brand dilution from ${domainMonitor.domain}`;
  }

  private assessSocialMediaPotentialDamage(
    socialMediaMonitor: SocialMediaMonitor
  ): string {
    const engagement = socialMediaMonitor.engagement;
    const reach = engagement ? engagement.views + engagement.shares : 0;

    if (reach > 10000) {
      return "High visibility threat with significant reach potential";
    }
    if (reach > 1000) {
      return "Medium visibility threat with moderate reach";
    }
    return "Low visibility threat with limited reach";
  }

  private assessSocialMediaBusinessImpact(
    socialMediaMonitor: SocialMediaMonitor
  ): string {
    return `${socialMediaMonitor.detectionType} on ${socialMediaMonitor.platform} affecting brand reputation`;
  }

  @Mock("ThreatDetectionService-findById")
  async findById(id: string): Promise<ThreatDetection | null> {
    return this.threatDetectionRepository.findById(id);
  }

  @Mock("ThreatDetectionService-getHighPriorityThreats")
  async getHighPriorityThreats(): Promise<ThreatDetection[]> {
    return this.threatDetectionRepository.findHighPriorityThreats();
  }

  @Mock("ThreatDetectionService-bulkUpdateStatus")
  async bulkUpdateStatus(
    threatIds: string[],
    status: ThreatDetection["status"]
  ): Promise<void> {
    return this.threatDetectionRepository.updateBulkStatus(threatIds, status);
  }

  @Mock("ThreatDetectionService-updateEvidence")
  async updateEvidence(
    id: string,
    evidence: any
  ): Promise<ThreatDetection | null> {
    return this.threatDetectionRepository.update(id, { evidence });
  }

  @Mock("ThreatDetectionService-getThreatTimeline")
  async getThreatTimeline(brandId: string, days: number = 30): Promise<any[]> {
    return this.threatDetectionRepository.getThreatTimeline(brandId, days);
  }
}
