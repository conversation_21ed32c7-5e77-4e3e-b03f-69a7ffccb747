import { Provider } from '@core/decorators/decorators';
import { NotificationChannel } from '../types/notification.types';

@Provider()
export class NotificationService {
  async send(userId: string, notification: {
    title: string;
    message: string;
    type: string;
    channels: NotificationChannel[];
  }) {
    for (const channel of notification.channels) {
      await this.sendToChannel(channel, userId, notification);
    }
  }

  async getUserPreferences(userId: string) {
    // Get user notification preferences
  }
}