export class Logger {
  constructor(private context: string) {}

  log(message: any) {
    console.log(`[${this.context}] ${message}`);
  }

  error(error: Error | string) {
    const message = error instanceof Error ? error.stack : error;
    console.error(`[${this.context}] ${message}`);
  }

  warn(message: any) {
    console.warn(`[${this.context}] ${message}`);
  }

  info(message: any) {
    console.info(`[${this.context}] ${message}`);
  }

  debug(message: any) {
    console.debug(`[${this.context}] ${message}`);
  }
}