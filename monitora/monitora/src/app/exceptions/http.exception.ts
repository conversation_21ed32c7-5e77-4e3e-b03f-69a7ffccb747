export class HttpException extends Error {
  constructor(
    public status: number,
    public message: string,
    public errors?: any[]
  ) {
    super(message);
    this.name = 'HttpException';
  }
}

export class ValidationException extends HttpException {
  constructor(errors: any[]) {
    super(400, 'Validation Error', errors);
    this.name = 'ValidationException';
  }
}

export class NotFoundException extends HttpException {
  constructor(resource: string) {
    super(404, `${resource} not found`);
    this.name = 'NotFoundException';
  }
}

export class UnauthorizedException extends HttpException {
  constructor(message = 'Unauthorized') {
    super(401, message);
    this.name = 'UnauthorizedException';
  }
}

export class ForbiddenException extends HttpException {
  constructor(message = 'Forbidden') {
    super(403, message);
    this.name = 'ForbiddenException';
  }
}