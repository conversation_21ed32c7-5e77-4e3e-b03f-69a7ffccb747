import express from 'express';
import { routes } from '../core/routes';
import { container } from '@core/di/container';
import { AppDataSource } from '../config/database';
import { errorHandler } from './middlewares/error.middleware';

declare global {
  namespace Express {
    interface Request {
      currentUser: { id: number; username: string };
    }
  }
}

const app = express();
container.register();

app.use(express.json());
app.use(routes());

// Error handling middleware should be last
app.use(errorHandler);

// Initialize TypeORM connection
AppDataSource.initialize()
  .then(() => {
    console.log('Database connection initialized');
    app.listen(3000, () => console.log('Server running on port 3000'));
  })
  .catch((error) => console.log('Error during Data Source initialization:', error));
