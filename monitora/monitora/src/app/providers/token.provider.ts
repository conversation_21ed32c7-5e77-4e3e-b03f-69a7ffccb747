import { Provider } from '@core/decorators/decorators';
import { <PERSON><PERSON> } from '@core/decorators/mock.decorator';
import * as jwt from 'jsonwebtoken';

const SECRET_KEY = 'secret_key';

@Provider()
export class TokenProvider {
  @Mock('TokenProvider-getToken')
  getToken(user: any) {
    return jwt.sign({ id: user.id, username: user.username }, SECRET_KEY, { expiresIn: '1h' });
  }

  @Mock('TokenProvider-verifyToken')
  verifyToken(token: string) {
    return jwt.verify(token, SECRET_KEY);
  }
}