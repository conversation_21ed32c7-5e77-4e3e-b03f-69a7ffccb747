import { createRequest, createResponse } from "node-mocks-http";
import { TodoController } from "../../app/controllers/todo.controller";
import { mock } from "../../core/mock/mock";
import { TodoGateway } from "../../app/gateways/todo.gateway";
import { container } from '../../core/di/container';

container.register();

mock(container, TodoGateway);

describe("TodoController", () => {
  let todoController: TodoController;

  beforeEach(() => {
    todoController = container.resolve(TodoController);
  });

  it("should return a todo with id 1", async () => {
    const req = createRequest({
      method: "GET",
      url: "/todos/1",
      params: { id: "1" },
    });
    const res = createResponse();

    await todoController.getTodo(req, res);
    const data = res._getJSONData();

    expect(data).toHaveProperty("id", 1);
  });
});