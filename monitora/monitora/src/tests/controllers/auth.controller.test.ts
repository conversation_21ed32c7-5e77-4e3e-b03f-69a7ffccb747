import { createRequest, createResponse } from 'node-mocks-http';
import { mock } from '../../core/mock/mock';
import { container } from '../../core/di/container';
import { AuthController } from '../../app/controllers/auth.controller';
import { TokenProvider } from '../../app/providers/token.provider';
import { UserRepository } from '../../app/repositories/user.repository';

container.register();
mock(container, TokenProvider);
mock(container, UserRepository);

describe('AuthController', () => {
  let authController: AuthController;

  beforeEach(() => {
    authController = container.resolve(AuthController);
  });

  it('should return a token', async () => {
    const req = createRequest({
      method: 'POST',
      url: '/auth/login',
      body: { username: 'tiago', password: '1234' },
    });
    const res = createResponse();

    await authController.login(req, res);
    const data = res._getJSONData();

    expect(data).toHaveProperty('success', true);
  });
});