import fs from "fs";
import path from "path";
import { config } from "@core/config";

export class TokenProviderMock {
  async getToken(...args: any[]): Promise<any> {
    const mockFilePath = path.join(config.recordDataFolder, 'TokenProvider-getToken.json');
    const data = fs.readFileSync(mockFilePath, { encoding: 'utf8' });
    return JSON.parse(data).returnValue;
  }

  async verifyToken(...args: any[]): Promise<any> {
    const mockFilePath = path.join(config.recordDataFolder, 'TokenProvider-verifyToken.json');
    const data = fs.readFileSync(mockFilePath, { encoding: 'utf8' });
    return JSON.parse(data).returnValue;
  }
}
