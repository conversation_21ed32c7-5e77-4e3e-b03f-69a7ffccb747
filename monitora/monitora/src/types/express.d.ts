// Extend Express Request interface to include user properties
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        username: string;
        email: string;
        name: string;
        defaultRole: string;
        roles?: any[];
      };
      currentUser?: {
        id: string;
        username: string;
        email: string;
        name: string;
        defaultRole: string;
        roles?: any[];
      };
    }
  }
}

export {};
