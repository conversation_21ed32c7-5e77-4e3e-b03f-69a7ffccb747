declare module "string-similarity" {
  export function compareTwoStrings(first: string, second: string): number;
  export function findBestMatch(
    mainString: string,
    targetStrings: string[]
  ): {
    ratings: Array<{ target: string; rating: number }>;
    bestMatch: { target: string; rating: number };
  };
}

declare module "yauzl" {
  // Basic type declarations for yauzl to resolve the missing type definition error
  export function open(
    path: string,
    options: any,
    callback: (err: any, zipfile: any) => void
  ): void;
  export function fromBuffer(
    buffer: Buffer,
    options: any,
    callback: (err: any, zipfile: any) => void
  ): void;
}
