declare module 'yauzl' {
  export function open(path: string, options: any, callback: (err: any, zipfile: any) => void): void;
  export function fromBuffer(buffer: Buffer, options: any, callback: (err: any, zipfile: any) => void): void;
  export function fromFd(fd: number, options: any, callback: (err: any, zipfile: any) => void): void;
  export function fromRandomAccessReader(reader: any, totalSize: number, options: any, callback: (err: any, zipfile: any) => void): void;
  export function dosDateTimeToDate(date: number, time: number): Date;
  export function validateFileName(fileName: string): string;
  
  export interface ZipFile {
    entryCount: number;
    comment: string;
    readEntry(): void;
    openReadStream(entry: Entry, callback: (err: any, readStream: any) => void): void;
    close(): void;
  }
  
  export interface Entry {
    fileName: string;
    extraFields: any[];
    comment: string;
    versionMadeBy: number;
    versionNeededToExtract: number;
    generalPurposeBitFlag: number;
    compressionMethod: number;
    lastModFileTime: number;
    lastModFileDate: number;
    crc32: number;
    compressedSize: number;
    uncompressedSize: number;
    fileNameLength: number;
    extraFieldLength: number;
    fileCommentLength: number;
    internalFileAttributes: number;
    externalFileAttributes: number;
    relativeOffsetOfLocalHeader: number;
    getLastModDate(): Date;
    isEncrypted(): boolean;
    isCompressed(): boolean;
  }
}
