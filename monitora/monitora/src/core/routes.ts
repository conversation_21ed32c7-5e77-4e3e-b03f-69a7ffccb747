import { Router } from 'express';
import { container } from '@core/di/container';
import {  controllers } from '@core/config';

const router = Router();

export function routes() {
  const files = controllers();

  files.forEach(modulePath => {
    const importedModule = require(modulePath);

    Object.values(importedModule).forEach((ControllerClass: any) => {
      const path: string = Reflect.getMetadata('path', ControllerClass);

      if (path) {
        const controllerInstance = container.resolve<any>(ControllerClass);
        const routes = Reflect.getMetadata('routes', ControllerClass) || [];
        const middlewares = Reflect.getMetadata('middlewares', ControllerClass) || [];

        routes.forEach((route: any) => {
          const routeMiddlewares = middlewares
            .filter((m: any) => m.handler === route.handler)
            .map((m: any) => m.middleware);

          const ref = router[route.method as keyof Router] as Function;
          const actionMethod = ref.bind(router);

          const fullPath = `${path}${route.path}`;
          const handler = controllerInstance[route.handler];

          actionMethod(fullPath, ...routeMiddlewares, handler.bind(controllerInstance));

          console.log(`\x1b[32mRegistered route: [${route.method.toUpperCase()}] ${fullPath}`, '\x1b[0m');
        });
      }
    });
  });

  return router;
}