import { config } from '@core/config';
import fs from 'fs';
import path from 'path';

export function Mock(identifier: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const result = await originalMethod.apply(this, args);

      if (process.env.MOCK_RECORD_MODE === 'true') {
        const transformedReturn = result;
        const transformedArgs = args;
        const recorded = {
          args: transformedArgs,
          returnValue: transformedReturn,
        };

        const mocksFolder = path.resolve(config.recordDataFolder);
        const mockFilePath = path.join(mocksFolder, `${identifier}.json`);

        if (!fs.existsSync(mocksFolder)) {
          fs.mkdirSync(mocksFolder, { recursive: true });
        }

        fs.writeFileSync(mockFilePath, JSON.stringify(recorded, null, 2), {
          encoding: 'utf8',
        });

        console.log(`Recorded mock data for ${identifier} at ${mockFilePath}`);
      }

      return result;
    };

    return descriptor;
  };
}
