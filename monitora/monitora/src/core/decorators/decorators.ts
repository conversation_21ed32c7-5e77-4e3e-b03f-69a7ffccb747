import 'reflect-metadata';

export function Provider() {
  return function (target: any) {
    Reflect.defineMetadata('custom:injectable', true, target);
  };
}

export function Controller(path: string) {
  return function (target: any) {
    Reflect.defineMetadata('path', path, target);
    Reflect.defineMetadata('custom:injectable', true, target);
  };
}

export function Get(path: string) {
  return function (target: any, key: string) {
    if (!Reflect.hasMetadata('routes', target.constructor)) {
      Reflect.defineMetadata('routes', [], target.constructor);
    }

    const routes = Reflect.getMetadata('routes', target.constructor);
    routes.push({ method: 'get', path, handler: key });

    Reflect.defineMetadata('routes', routes, target.constructor);
  };
}

export function Post(path: string) {
  return function (target: any, key: string) {
    if (!Reflect.hasMetadata('routes', target.constructor)) {
      Reflect.defineMetadata('routes', [], target.constructor);
    }

    const routes = Reflect.getMetadata('routes', target.constructor);
    routes.push({ method: 'post', path, handler: key });

    Reflect.defineMetadata('routes', routes, target.constructor);
  };
}

export function Put(path: string) {
  return function (target: any, key: string) {
    if (!Reflect.hasMetadata('routes', target.constructor)) {
      Reflect.defineMetadata('routes', [], target.constructor);
    }

    const routes = Reflect.getMetadata('routes', target.constructor);
    routes.push({ method: 'put', path, handler: key });

    Reflect.defineMetadata('routes', routes, target.constructor);
  };
}

export function Delete(path: string) {
  return function (target: any, key: string) {
    if (!Reflect.hasMetadata('routes', target.constructor)) {
      Reflect.defineMetadata('routes', [], target.constructor);
    }

    const routes = Reflect.getMetadata('routes', target.constructor);
    routes.push({ method: 'delete', path, handler: key });

    Reflect.defineMetadata('routes', routes, target.constructor);
  };
}

export function Middleware(middleware: any) {
  return function (target: any, key: string) {
    if (!Reflect.hasMetadata('middlewares', target.constructor)) {
      Reflect.defineMetadata('middlewares', [], target.constructor);
    }

    const middlewares = Reflect.getMetadata('middlewares', target.constructor);
    middlewares.push({ handler: key, middleware });

    Reflect.defineMetadata('middlewares', middlewares, target.constructor);
  };
}
