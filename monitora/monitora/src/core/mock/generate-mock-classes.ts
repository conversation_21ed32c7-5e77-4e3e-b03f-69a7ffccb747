import { Project, MethodDeclaration, IndentationText } from 'ts-morph';
import * as fs from 'fs';
import * as path from 'path';
import { config } from '@core/config';
import { transformFileName } from '@core/utils/string.util';

const sourceFolders: string[] = config.sourceInjectionPaths;
const outputFolder: string = config.mockOutputFolder

if (!fs.existsSync(outputFolder)) {
  fs.mkdirSync(outputFolder, { recursive: true });
}

const project = new Project({
  tsConfigFilePath: 'tsconfig.json',
  manipulationSettings: {
    indentationText: IndentationText.TwoSpaces,
  },
});

function processSourceFile(filePath: string) {
  const sourceFile = project.addSourceFileAtPath(filePath);

  sourceFile.getClasses().forEach(classDeclaration => {
    const originalClassName = classDeclaration.getName();
    if (!originalClassName) return;

    const mockClassName = `${originalClassName}Mock`;
    const outputFilePath = path.join(outputFolder, `${transformFileName(originalClassName)}.mock.ts`);

    let mockFile = project.getSourceFile(outputFilePath);

    if (!mockFile) {
      mockFile = project.createSourceFile(outputFilePath, '', { overwrite: true });
      mockFile.addImportDeclaration({
        moduleSpecifier: 'fs',
        defaultImport: 'fs'
      });
      mockFile.addImportDeclaration({
        moduleSpecifier: 'path',
        defaultImport: 'path'
      });
      mockFile.addImportDeclaration({
        moduleSpecifier: "@core/config",
        namedImports: ["config"]
      });
      mockFile.addClass({
        name: mockClassName,
        isExported: true
      });
    }
    
    const mockClass = mockFile.getClass(mockClassName);
    if (!mockClass) return;

    const methods = classDeclaration.getMethods().filter((method: MethodDeclaration) => 
      method.getDecorator('Mock')
    );

    if(!methods.length) {
      project.removeSourceFile(mockFile);

      return;
    }

    methods.forEach((method: MethodDeclaration) => {
      const methodName = method.getName();
      const methodImpl = `const mockFilePath = path.join(config.recordDataFolder, '${originalClassName}-${methodName}.json');
const data = fs.readFileSync(mockFilePath, { encoding: 'utf8' });
return JSON.parse(data).returnValue;`;

      let existingMethod = mockClass.getMethod(methodName);

      if(!existingMethod) {
        mockClass.addMethod({
          name: methodName,
          isAsync: true,
          parameters: [{ name: '...args', type: 'any[]' }],
          returnType: 'Promise<any>',
          statements: methodImpl
        });

        console.log(`Added method ${methodName} to ${originalClassName}.mock.ts`);
      }
    });
    
    mockFile.saveSync();
    console.log(`Processed mock file for class ${originalClassName}: ${outputFilePath}`);
  });
}

function getTSFiles(folder: string): string[] {
  let results: string[] = [];
  const list = fs.readdirSync(folder);
  
  list.forEach(file => {
    const filePath = path.join(folder, file);
    const stat = fs.statSync(filePath);
    if (stat && stat.isDirectory()) {
      results = results.concat(getTSFiles(filePath));
    } else if (file.endsWith('.ts') && !file.endsWith('.d.ts')) {
      results.push(filePath);
    }
  });

  return results;
}

sourceFolders.forEach(folderPath => {
  const tsFiles = getTSFiles(folderPath);
  tsFiles.forEach(filePath => {
    console.log(`Processing file: ${filePath}`);
    processSourceFile(filePath);
  });
});

project.save().then(() => {
  console.log('All mock files generated/updated.');
});