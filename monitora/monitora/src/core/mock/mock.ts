import * as path from 'path';
import { Constructor, DIContainer } from '@core/di/container';
import { transformFileName } from '../utils/string.util';
import { config } from '@core/config';

export function mock<T>(container: D<PERSON>ontainer, prodClass: Constructor<T>): T {
  const baseName = prodClass.name;
  const fileName = transformFileName(baseName);

  const mockFilePath = path.join(config.mockOutputFolder, `${fileName}.mock`);
  const mockModule = require(mockFilePath);
  const mockClassName = `${baseName}Mock`;
  const MockClass = mockModule[mockClassName];

  if (!MockClass) {
    throw new Error(`Mock class ${mockClassName} not found in file ${mockFilePath}`);
  }

  container.override(prodClass, new MockClass());

  return new MockClass();
}