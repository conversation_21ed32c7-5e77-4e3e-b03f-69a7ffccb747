import axios from 'axios';

export async function generateMockDataWithAI(data: any): Promise<any> {
  const inputData = JSON.stringify(data, null, 2);
  const prompt = `Given the following JSON data, create a fake version 
  by replacing real names, emails, addresses, and ids with realistic,
  fictional values while preserving the structure.
  
Data:
${inputData}

Fake Data:`;

  try {
    const response = await axios.post(
      'https://api.openai.com/v1/completions',
      {
        model: 'text-davinci-003',
        prompt,
        max_tokens: 500,
        temperature: 0.7,
        top_p: 1,
        n: 1,
        stop: null,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
        },
      }
    );
    const aiText = response.data.choices[0].text;
    return JSON.parse(aiText);
  } catch (error) {
    console.error('Error generating fake data with AI:', error);
    return data;
  }
}