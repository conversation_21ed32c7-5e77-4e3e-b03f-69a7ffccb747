export function transformFileName(fileName: string): string {
  const extIndex = fileName.lastIndexOf('.');
  let base: string;
  let ext: string = '';

  if (extIndex !== -1) {
    base = fileName.substring(0, extIndex);
    ext = fileName.substring(extIndex);
  } else {
    base = fileName;
  }

  const words = base.match(/[A-Z][a-z0-9]*/g);

  if (!words) {
    return fileName.toLowerCase();
  }

  if (words.length === 1) {
    return fileName.toLowerCase();
  }

  const head = words
    .slice(0, -1)
    .map((w) => w.toLowerCase())
    .join('-');
  const tail = words[words.length - 1].toLowerCase();

  return `${head}.${tail}${ext}`;
}
