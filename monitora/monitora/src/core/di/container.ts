import { config } from '@core/config';
import { globSync } from 'glob';
import 'reflect-metadata';
import * as path from 'path';

export type Constructor<T = any> = new (...args: any[]) => T;

export class DIContainer {
  private bindings = new Map<Constructor, { instance?: any }>();

  register() {
    const foldersToRegister = config.sourceInjectionPaths;
    
    foldersToRegister.forEach((folder) => {
      const pattern = path.join(folder, '**', '!(*.d).ts');
      const files = globSync(pattern, { absolute: true });
    
      files.forEach((file) => {
        const moduleExports = require(file);
        Object.keys(moduleExports).forEach((exportKey) => {
          const exported = moduleExports[exportKey];
          if (
            typeof exported === 'function' &&
            Reflect.getMetadata('custom:injectable', exported) !== undefined
          ) {
            this.add(exported as Constructor);
            console.info(`\x1b[34mRegistered DI: ${exportKey}`, '\x1b[0m');
          }
        });
      });
    });
  }

  add<T>(token: Constructor<T>): void {
    this.bindings.set(token, {});
  }

  resolve<T>(token: Constructor<T>): T {
    const binding = this.bindings.get(token);
    if (!binding) {
      throw new Error(`No binding found for ${token.name}`);
    }
    if (binding.instance) {
      return binding.instance;
    }
    const paramTypes: Constructor[] = Reflect.getMetadata('design:paramtypes', token) || [];
    const dependencies = paramTypes.map((dep) => this.resolve(dep));
    const instance = new token(...dependencies);
    binding.instance = instance;

    return instance;
  }

  override<T>(token: Constructor<T>, instance: T): void {
    this.bindings.set(token, { instance });
  }
}

export const container = new DIContainer();