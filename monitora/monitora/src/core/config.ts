import * as fs from 'fs';
import * as path from 'path';

export interface Config {
  controllersFolder: string;
  sourceInjectionPaths: string[];
  mockOutputFolder: string;
  recordDataFolder: string;
}

const rootPath = process.cwd();
const configPath = path.join(rootPath, 'boost.config.json');

if (!fs.existsSync(configPath)) {
  throw new Error(`Config file not found at: ${configPath}`);
}

const raw = fs.readFileSync(configPath, 'utf8');
const config: Config = JSON.parse(raw) as Config;

Object.keys(config).forEach((key) => {
  const value = config[key as keyof Config];
  if (typeof value === 'string') {
    config[key as keyof Config] = path.join(rootPath, value) as any;
  } else {
    value.forEach((v: string, i: number) => {
      value[i] = path.join(rootPath, v);
    });
  }
});

export { config };

export function controllers() {
  const controllersPath = path.resolve(config.controllersFolder);
  const files = fs
    .readdirSync(controllersPath)
    .filter((file) => file.endsWith('.ts') || file.endsWith('.js'));

  return files.map((file) => path.join(controllersPath, file));
}
