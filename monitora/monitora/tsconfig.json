{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "declaration": true,
    "outDir": "./dist",
    "strict": true,
    "strictPropertyInitialization": false,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "rootDir": "src",
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "typeRoots": ["./src/types/*", "./node_modules/@types"],
    "inlineSourceMap": true,
    "inlineSources": true,
    "baseUrl": "./",
    "paths": {
      "@core/*": ["src/core/*"],
    },
  },
  "exclude": ["node_modules", "dist"],
  "include": ["src/**/*", "src/**/*.d.ts"],
}
