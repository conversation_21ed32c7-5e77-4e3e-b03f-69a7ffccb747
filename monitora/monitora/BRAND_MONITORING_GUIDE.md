# 🛡️ Complete Brand Monitoring Guide

## 📋 **Multiple Domains Support**

### ✅ **How to Register Multiple Domains**

Your brand can monitor multiple domains simultaneously:

```bash
# Create a brand with primary domain
POST /brands
{
  "name": "My Company",
  "primaryDomain": "mycompany.com",
  "additionalDomains": ["mycompany.net", "mycompany.org"],
  "keywords": ["mycompany", "my company", "myco"]
}

# Add more domains later
POST /brands/:brandId/domains
{
  "domain": "mycompany.co.uk"
}

# Get all domains for a brand
GET /brands/:brandId/domains
```

### 🎯 **What Gets Monitored**
- **Primary Domain**: Main company domain
- **Additional Domains**: All subsidiary/regional domains
- **Variations**: Automatic detection of typosquatting across ALL domains
- **Keywords**: Brand terms that appear in suspicious domains

---

## 🏢 **Brand Information Structure**

### 📊 **Complete Brand Profile**

```json
{
  "name": "Apple Inc.",
  "description": "Technology company",
  "primaryDomain": "apple.com",
  "additionalDomains": [
    "apple.co.uk",
    "apple.de", 
    "apple.fr",
    "icloud.com"
  ],
  "keywords": [
    "apple",
    "iphone", 
    "ipad",
    "macbook",
    "ios"
  ],
  "protectedTerms": [
    "apple",
    "apple inc",
    "cupertino"
  ],
  "logoUrl": "https://apple.com/logo.png",
  "socialMediaProfiles": [
    {
      "platform": "twitter",
      "username": "apple",
      "url": "https://twitter.com/apple",
      "verified": true
    },
    {
      "platform": "facebook", 
      "username": "apple",
      "url": "https://facebook.com/apple",
      "verified": true
    }
  ],
  "contactInfo": {
    "email": "<EMAIL>",
    "phone": "******-APL-CARE",
    "address": "1 Apple Park Way, Cupertino, CA"
  },
  "monitoringSettings": {
    "domainMonitoring": true,
    "socialMediaMonitoring": true,
    "threatDetection": true,
    "alertFrequency": "realtime",
    "sensitivityLevel": "high"
  }
}
```

---

## 🚨 **How to Identify Scam Ads**

### 🔍 **Automated Detection Indicators**

The system automatically flags ads based on:

#### **1. Brand Impersonation Signals**
```
✅ DETECTED: Ad contains "iPhone 15 Pro Max - 90% OFF!"
❌ SCAM INDICATOR: Uses Apple product names without authorization
🎯 RISK SCORE: 85/100
```

#### **2. Suspicious URL Patterns**
```
✅ DETECTED: Ad links to "apple-store-sale.tk"
❌ SCAM INDICATOR: Contains "apple" but not official domain
🎯 RISK SCORE: 95/100
```

#### **3. Content Analysis**
```
✅ DETECTED: "Congratulations! You've won a free iPhone!"
❌ SCAM INDICATORS:
   - Fake giveaway language
   - Too-good-to-be-true offer
   - Urgency tactics
🎯 RISK SCORE: 90/100
```

### 🎯 **Manual Verification Checklist**

When reviewing flagged ads, check:

#### **Visual Elements**
- [ ] Uses your logo or similar imagery
- [ ] Claims to be official/authorized
- [ ] Has fake verification badges
- [ ] Poor quality graphics/text

#### **Content Red Flags**
- [ ] Extreme discounts (>80% off)
- [ ] "Limited time" pressure tactics
- [ ] Requests personal information
- [ ] Fake contest/giveaway

#### **Technical Indicators**
- [ ] Suspicious domain in ad URL
- [ ] Very short campaign duration
- [ ] Low ad spend (test campaign)
- [ ] Unverified advertiser account

#### **Platform-Specific Signs**

**Facebook/Instagram:**
```json
{
  "redFlags": {
    "lowSpend": "< $100 total spend",
    "shortDuration": "< 3 days campaign",
    "unverifiedPage": "No blue checkmark",
    "newAccount": "Created recently"
  }
}
```

**Google Ads:**
```json
{
  "redFlags": {
    "suspiciousKeywords": "Contains brand terms",
    "landingPageMismatch": "Ad content ≠ landing page",
    "poorQualityScore": "Low Google quality rating"
  }
}
```

---

## 🤖 **Automated Scam Detection Process**

### 📊 **Detection Algorithm**

```javascript
// Risk Scoring System
const riskFactors = {
  brandTermsWithoutAuth: +30,
  suspiciousURL: +40, 
  scamKeywords: +15,
  fakePromotion: +35,
  urgencyTactics: +20,
  unrealisticOffer: +25
};

// Classification
if (riskScore >= 80) return "CRITICAL_SCAM";
if (riskScore >= 60) return "HIGH_RISK"; 
if (riskScore >= 40) return "MEDIUM_RISK";
return "LOW_RISK";
```

### 🔄 **Monitoring Workflow**

1. **Continuous Scanning**
   ```
   Every Hour: Scan for new ads containing brand keywords
   Every 30min: Analyze flagged content for scam indicators  
   Real-time: Alert on critical threats
   ```

2. **Multi-Platform Coverage**
   ```
   ✅ Facebook Ads Library
   ✅ Instagram Sponsored Posts
   ✅ Google Ads (via search)
   ✅ Twitter Promoted Tweets
   ✅ YouTube Video Ads
   ```

3. **Evidence Collection**
   ```
   📸 Screenshots of ads
   🔗 Target URL analysis
   📊 Engagement metrics
   📋 Advertiser information
   ⏰ Campaign duration data
   ```

---

## 📱 **API Usage Examples**

### 🔍 **Scan for Scam Ads**
```bash
# Scan all platforms for scam ads
POST /ad-detection/scan/:brandId
Response: {
  "found": 3,
  "scamAds": [
    {
      "platform": "facebook",
      "adId": "fb_123456",
      "content": "iPhone 15 Pro Max - 95% OFF Limited Time!",
      "targetUrl": "apple-deals.tk",
      "riskScore": 95,
      "scamIndicators": [
        "Unrealistic discount",
        "Suspicious target URL", 
        "Uses brand terms without authorization"
      ],
      "isScam": true
    }
  ]
}
```

### 📊 **Get Detection Stats**
```bash
GET /ad-detection/stats/:brandId
Response: {
  "totalScansPerformed": 1250,
  "scamAdsDetected": 47,
  "adsReported": 23,
  "platformBreakdown": {
    "facebook": { "scanned": 450, "detected": 18 },
    "google": { "scanned": 380, "detected": 12 },
    "instagram": { "scanned": 420, "detected": 17 }
  }
}
```

### 🚨 **Report Scam Ad**
```bash
POST /ad-detection/report
{
  "platform": "facebook",
  "adId": "fb_123456", 
  "reason": "Brand impersonation with fake discount",
  "brandId": "brand-uuid"
}
```

---

## 🎯 **Best Practices**

### ✅ **Setup Recommendations**

1. **Comprehensive Keywords**
   ```json
   {
     "keywords": [
       "exact-brand-name",
       "common-misspellings", 
       "product-names",
       "slogans",
       "executive-names"
     ]
   }
   ```

2. **Multiple Domain Registration**
   ```json
   {
     "domains": [
       "primary.com",
       "regional-variants.co.uk", 
       "common-typos.com",
       "product-domains.com"
     ]
   }
   ```

3. **Social Media Profiles**
   ```json
   {
     "verifiedProfiles": [
       "All official accounts with verification",
       "Regional/language variants",
       "Product-specific accounts"
     ]
   }
   ```

### 🚨 **Alert Configuration**

```json
{
  "alertSettings": {
    "critical": "immediate-email-sms",
    "high": "within-1-hour", 
    "medium": "daily-digest",
    "low": "weekly-report"
  }
}
```

---

## 🔧 **Integration Setup**

### 🔑 **Required API Keys**

```bash
# Meta (Facebook/Instagram)
META_ACCESS_TOKEN=your_token_here

# Google (Search/YouTube) 
GOOGLE_SEARCH_API_KEY=your_key_here
YOUTUBE_API_KEY=your_key_here

# Twitter
TWITTER_BEARER_TOKEN=your_token_here
```

### 🚀 **Quick Start**

1. **Create Brand**
2. **Add Domains & Keywords** 
3. **Configure Monitoring Settings**
4. **Start Automated Scanning**
5. **Review & Report Threats**

The system provides **comprehensive protection** across all major advertising platforms with **intelligent scam detection** and **automated reporting capabilities**!
